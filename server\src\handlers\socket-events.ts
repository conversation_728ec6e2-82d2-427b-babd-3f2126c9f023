import { nanoid } from 'nanoid';
import { v4 as uuidv4 } from 'uuid';
import { Server } from 'socket.io';
import { 
  SOCKET_ACTIONS,
  CustomSocket,
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
} from '../../../lib/types/socket';
import { 
  Room, 
  User, 
  ServerMessageType, 
  ServerMessage,
  ChatMessage,
  GameQuestion
} from '../../../lib/types/multiplayer';
import { roomsManager } from '../utils/room-management';
import { usersManager, requestIsNotFromHost, validateUsername } from '../utils/user-management';
import { GameLogic } from '../utils/game-logic';

const roomTimeouts: { [roomId: string]: NodeJS.Timeout | undefined } = {};

export const handleSocketEvents = (
  io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  socket: CustomSocket
) => {
  const userId = socket.handshake.auth.token;
  const adminTokenHandshake = socket.handshake.auth.adminToken;
  const adminToken = process.env.ADMIN_TOKEN;

  if (!userId) {
    socket.disconnect();
    return;
  }

  // Check if user is already connected
  if (usersManager.users.has(userId)) {
    socket.disconnect();
    return;
  }

  // Initialize socket data
  socket.data = {
    userId: userId || '',
    roomId: '',
    isAdmin: false,
  };

  if (typeof socket.data.userId !== 'string') return;

  // Check for admin privileges
  if (adminToken === adminTokenHandshake) {
    socket.data.isAdmin = true;
    socket.emit(SOCKET_ACTIONS.SET_ADMIN);
  }

  console.log(`⚡️ New user connected - User Id: ${userId}`);

  // Room management events
  socket.on(SOCKET_ACTIONS.CHECK_IF_ROOM_EXISTS, (roomId, callback) => {
    const room = roomsManager.get(roomId);
    typeof callback === 'function' && callback(room ?? null);
  });

  socket.on(SOCKET_ACTIONS.CREATE_ROOM, async (username, roomName, settings, callback) => {
    const usernameValidation = validateUsername(username);
    if (!usernameValidation.valid) {
      typeof callback === 'function' && callback({ error: usernameValidation.error });
      return;
    }

    const newRoomId = nanoid(6);
    
    if (userId && socket.data.userId) {
      const existingRoom = roomsManager.get(newRoomId);
      if (existingRoom) {
        typeof callback === 'function' && callback({ error: 'Room already exists' });
        return;
      }

      const user = usersManager.createUser({
        id: socket.data.userId,
        username,
        roomId: newRoomId,
        socketId: socket.id,
        isAdmin: socket.data.isAdmin,
      });

      console.log(`👀 New user created room: ${newRoomId} - User Id: ${userId}`);
      
      socket.join(newRoomId);
      socket.data.roomId = newRoomId;

      const newRoom = roomsManager.create(newRoomId, roomName, user, settings);
      
      if (newRoom) {
        typeof callback === 'function' && callback({ result: newRoom });
        socket.emit(SOCKET_ACTIONS.GET_ROOM_INFO, newRoom);
        socket.emit(SOCKET_ACTIONS.GET_USER_INFO, user);
      }
    } else {
      typeof callback === 'function' && callback({ error: 'Failed to create room' });
    }
  });

  socket.on(SOCKET_ACTIONS.JOIN_ROOM, (roomId, username, callback) => {
    const usernameValidation = validateUsername(username);
    if (!usernameValidation.valid) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: usernameValidation.error 
      });
      return;
    }

    if (!roomId || !socket.data.userId) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Invalid room ID or user ID' 
      });
      return;
    }

    if (roomId.length !== 6) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Room ID must be 6 characters long' 
      });
      return;
    }

    const existingRoom = roomsManager.has(roomId);
    if (!existingRoom) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: `Room ${roomId} not found` 
      });
      return;
    }

    const updatedRoom = addUserToRoom(io, socket, socket.data.userId, roomId, username);
    if (updatedRoom && typeof callback === 'function') {
      callback({ success: true });
    }
  });

  socket.on(SOCKET_ACTIONS.JOIN_ROOM_BY_INVITE, (inviteCode, username, roomPasscode, callback) => {
    const usernameValidation = validateUsername(username);
    if (!usernameValidation.valid) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: usernameValidation.error 
      });
      return;
    }

    if (!inviteCode || !socket.data.userId) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Invalid invite code or user ID' 
      });
      return;
    }

    if (inviteCode.length !== 5) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Invite code must be 5 characters long' 
      });
      return;
    }

    const room = roomsManager.getRoomByInviteCode(inviteCode);
    if (!room) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Invalid invite code or room no longer exists' 
      });
      return;
    }

    if (room.passcode && roomPasscode.trim() !== room.passcode) {
      typeof callback === 'function' && callback({ 
        success: false, 
        error: 'Incorrect passcode' 
      });
      return;
    }

    const updatedRoom = addUserToRoom(io, socket, socket.data.userId, room.id, username);
    if (updatedRoom && typeof callback === 'function') {
      callback({ success: true, roomId: room.id });
    }
  });

  // Continue in next part due to length limit...
