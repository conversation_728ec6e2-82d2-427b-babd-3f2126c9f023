"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { nanoid } from 'nanoid';
import { Difficulty } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

export default function MultiplayerLobby() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'create' | 'join'>('create');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Create room form state
  const [roomName, setRoomName] = useState('');
  const [username, setUsername] = useState('');
  const [maxPlayers, setMaxPlayers] = useState(4);
  const [difficulty, setDifficulty] = useState<Difficulty>('medium');
  const [questionsCount, setQuestionsCount] = useState(10);
  const [timePerQuestion, setTimePerQuestion] = useState(15);
  
  // Join room form state
  const [joinRoomCode, setJoinRoomCode] = useState('');
  const [joinUsername, setJoinUsername] = useState('');

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Generate session token and room code
      const sessionToken = `user_${Date.now()}_${nanoid(8)}`;
      const roomCode = nanoid(6).toUpperCase();

      // Store session data
      localStorage.setItem('multiplayer_session', JSON.stringify({
        sessionToken,
        username,
        roomCode,
        isHost: true
      }));

      // Navigate to room page
      router.push('/multiplayer/room');
    } catch (err) {
      setError('Failed to create room. Please try again.');
      console.error('Create room error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Generate session token
      const sessionToken = `user_${Date.now()}_${nanoid(8)}`;

      // Store session data
      localStorage.setItem('multiplayer_session', JSON.stringify({
        sessionToken,
        username: joinUsername,
        roomCode: joinRoomCode.toUpperCase(),
        isHost: false
      }));

      // Navigate to room page
      router.push('/multiplayer/room');
    } catch (err) {
      setError('Failed to join room. Please try again.');
      console.error('Join room error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Tab Navigation */}
      <Card className="mb-6 shadow-card">
        <CardContent className="p-1">
          <div className="flex">
            <Button
              variant={activeTab === 'create' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('create')}
              className="flex-1 rounded-md"
            >
              🏗️ Create Room
            </Button>
            <Button
              variant={activeTab === 'join' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('join')}
              className="flex-1 rounded-md"
            >
              🚪 Join Room
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Create Room Form */}
      {activeTab === 'create' && (
        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-foreground mb-2">Create New Room</h2>
              <p className="text-muted-foreground text-sm">
                Set up a new multiplayer game room
              </p>
            </div>
            
            <form onSubmit={handleCreateRoom} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Your Username
                </label>
                <Input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Enter your username"
                  required
                  maxLength={20}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Room Name
                </label>
                <Input
                  type="text"
                  value={roomName}
                  onChange={(e) => setRoomName(e.target.value)}
                  placeholder="Enter room name"
                  required
                  maxLength={30}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Max Players
                  </label>
                  <Input
                    type="number"
                    value={maxPlayers}
                    onChange={(e) => setMaxPlayers(Number(e.target.value))}
                    min={2}
                    max={8}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Questions
                  </label>
                  <Input
                    type="number"
                    value={questionsCount}
                    onChange={(e) => setQuestionsCount(Number(e.target.value))}
                    min={5}
                    max={50}
                    required
                  />
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                size="lg"
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Create Room'}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Join Room Form */}
      {activeTab === 'join' && (
        <Card className="shadow-card">
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-foreground mb-2">Join Room</h2>
              <p className="text-muted-foreground text-sm">
                Enter a room code to join an existing game
              </p>
            </div>
            
            <form onSubmit={handleJoinRoom} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Your Username
                </label>
                <Input
                  type="text"
                  value={joinUsername}
                  onChange={(e) => setJoinUsername(e.target.value)}
                  placeholder="Enter your username"
                  required
                  maxLength={20}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Room Code
                </label>
                <Input
                  type="text"
                  value={joinRoomCode}
                  onChange={(e) => setJoinRoomCode(e.target.value.toUpperCase())}
                  placeholder="Enter 6-character room code"
                  required
                  maxLength={6}
                  className="uppercase"
                />
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                size="lg"
                disabled={isLoading}
              >
                {isLoading ? 'Joining...' : 'Join Room'}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
