"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { nanoid } from 'nanoid';
import { Difficulty } from '@/lib/constants';

interface CreateRoomForm {
  username: string;
  roomName: string;
  difficulty: Difficulty;
  questionCount: number;
  timePerQuestion: number;
  allowSpectators: boolean;
}

interface JoinRoomForm {
  username: string;
  roomId: string;
  inviteCode: string;
  passcode: string;
}

export default function MultiplayerLobby() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'create' | 'join'>('create');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [createForm, setCreateForm] = useState<CreateRoomForm>({
    username: '',
    roomName: '',
    difficulty: 'easy',
    questionCount: 10,
    timePerQuestion: 30,
    allowSpectators: true,
  });

  const [joinForm, setJoinForm] = useState<JoinRoomForm>({
    username: '',
    roomId: '',
    inviteCode: '',
    passcode: '',
  });

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Generate session token
      const sessionToken = `user_${Date.now()}_${nanoid(8)}`;
      
      // Store session token and form data in localStorage for the room page
      localStorage.setItem('sessionToken', sessionToken);
      localStorage.setItem('username', createForm.username);
      localStorage.setItem('roomAction', 'create');
      localStorage.setItem('roomSettings', JSON.stringify({
        roomName: createForm.roomName,
        difficulty: createForm.difficulty,
        questionCount: createForm.questionCount,
        timePerQuestion: createForm.timePerQuestion,
        allowSpectators: createForm.allowSpectators,
      }));

      // Navigate to room page
      router.push('/multiplayer/room');
    } catch (err) {
      setError('Failed to create room. Please try again.');
      console.error('Create room error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Generate session token
      const sessionToken = `user_${Date.now()}_${nanoid(8)}`;
      
      // Store session token and form data in localStorage for the room page
      localStorage.setItem('sessionToken', sessionToken);
      localStorage.setItem('username', joinForm.username);
      localStorage.setItem('roomAction', 'join');
      
      if (joinForm.roomId) {
        localStorage.setItem('roomId', joinForm.roomId);
      } else if (joinForm.inviteCode) {
        localStorage.setItem('inviteCode', joinForm.inviteCode);
        if (joinForm.passcode) {
          localStorage.setItem('roomPasscode', joinForm.passcode);
        }
      }

      // Navigate to room page
      router.push('/multiplayer/room');
    } catch (err) {
      setError('Failed to join room. Please try again.');
      console.error('Join room error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Tab Navigation */}
      <div className="flex mb-6 bg-white rounded-lg shadow-sm p-1">
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 py-3 px-6 rounded-md font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-blue-500 text-white shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          🏗️ Create Room
        </button>
        <button
          onClick={() => setActiveTab('join')}
          className={`flex-1 py-3 px-6 rounded-md font-medium transition-colors ${
            activeTab === 'join'
              ? 'bg-blue-500 text-white shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          🚪 Join Room
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Create Room Form */}
      {activeTab === 'create' && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Create New Room</h2>
          
          <form onSubmit={handleCreateRoom} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Username
              </label>
              <input
                type="text"
                value={createForm.username}
                onChange={(e) => setCreateForm({ ...createForm, username: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your username"
                required
                maxLength={20}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Room Name
              </label>
              <input
                type="text"
                value={createForm.roomName}
                onChange={(e) => setCreateForm({ ...createForm, roomName: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter room name"
                required
                maxLength={50}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Difficulty
                </label>
                <select
                  value={createForm.difficulty}
                  onChange={(e) => setCreateForm({ ...createForm, difficulty: e.target.value as Difficulty })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                  <option value="expert">Expert</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Questions
                </label>
                <select
                  value={createForm.questionCount}
                  onChange={(e) => setCreateForm({ ...createForm, questionCount: parseInt(e.target.value) })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>5 Questions</option>
                  <option value={10}>10 Questions</option>
                  <option value={15}>15 Questions</option>
                  <option value={20}>20 Questions</option>
                  <option value={25}>25 Questions</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Time per Question: {createForm.timePerQuestion}s
              </label>
              <input
                type="range"
                min="10"
                max="60"
                step="5"
                value={createForm.timePerQuestion}
                onChange={(e) => setCreateForm({ ...createForm, timePerQuestion: parseInt(e.target.value) })}
                className="w-full"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowSpectators"
                checked={createForm.allowSpectators}
                onChange={(e) => setCreateForm({ ...createForm, allowSpectators: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="allowSpectators" className="ml-2 block text-sm text-gray-700">
                Allow spectators
              </label>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              {isLoading ? 'Creating Room...' : 'Create Room'}
            </button>
          </form>
        </div>
      )}

      {/* Join Room Form */}
      {activeTab === 'join' && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Join Existing Room</h2>
          
          <form onSubmit={handleJoinRoom} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Username
              </label>
              <input
                type="text"
                value={joinForm.username}
                onChange={(e) => setJoinForm({ ...joinForm, username: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your username"
                required
                maxLength={20}
              />
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Room ID (6 characters)
                </label>
                <input
                  type="text"
                  value={joinForm.roomId}
                  onChange={(e) => setJoinForm({ ...joinForm, roomId: e.target.value.toUpperCase(), inviteCode: '' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ABC123"
                  maxLength={6}
                />
              </div>

              <div className="text-center text-gray-500 font-medium">OR</div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Invite Code (5 characters)
                </label>
                <input
                  type="text"
                  value={joinForm.inviteCode}
                  onChange={(e) => setJoinForm({ ...joinForm, inviteCode: e.target.value.toUpperCase(), roomId: '' })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="XYZ12"
                  maxLength={5}
                />
              </div>

              {joinForm.inviteCode && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Room Passcode (if required)
                  </label>
                  <input
                    type="password"
                    value={joinForm.passcode}
                    onChange={(e) => setJoinForm({ ...joinForm, passcode: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter passcode if room is private"
                  />
                </div>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading || (!joinForm.roomId && !joinForm.inviteCode)}
              className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              {isLoading ? 'Joining Room...' : 'Join Room'}
            </button>
          </form>
        </div>
      )}
    </div>
  );
}
