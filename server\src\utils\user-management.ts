import { User } from '../../../lib/types/multiplayer';
import { CustomSocket } from '../../../lib/types/socket';
import { roomsManager } from './room-management';

// Generate a random color for users
const generateUserColor = (): string => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

interface CreateUserParams {
  id: string;
  username: string;
  roomId: string;
  socketId: string;
  isAdmin?: boolean;
}

class UserManager {
  public users = new Map<string, User>();

  createUser(params: CreateUserParams): User {
    const user: User = {
      id: params.id,
      socketId: params.socketId,
      username: params.username,
      roomId: params.roomId,
      created: new Date().toISOString(),
      color: generateUserColor(),
      isAdmin: params.isAdmin || false,
      score: 0,
      isReady: false,
    };

    this.users.set(user.id, user);
    return user;
  }

  get(userId: string): User | undefined {
    return this.users.get(userId);
  }

  has(userId: string): boolean {
    return this.users.has(userId);
  }

  set(userId: string, user: User): void {
    this.users.set(userId, user);
  }

  delete(userId: string): boolean {
    return this.users.delete(userId);
  }

  setUsers(users: Map<string, User>): void {
    this.users = users;
  }

  getLength(): number {
    return this.users.size;
  }

  updateUser(userId: string, updates: Partial<User>): User | null {
    const user = this.get(userId);
    if (!user) return null;

    const updatedUser = { ...user, ...updates };
    this.set(userId, updatedUser);
    return updatedUser;
  }

  updateUserScore(userId: string, points: number): User | null {
    const user = this.get(userId);
    if (!user) return null;

    const updatedUser = { ...user, score: user.score + points };
    this.set(userId, updatedUser);
    return updatedUser;
  }

  setUserReady(userId: string, isReady: boolean): User | null {
    const user = this.get(userId);
    if (!user) return null;

    const updatedUser = { ...user, isReady };
    this.set(userId, updatedUser);
    return updatedUser;
  }

  resetUserScore(userId: string): User | null {
    const user = this.get(userId);
    if (!user) return null;

    const updatedUser = { ...user, score: 0 };
    this.set(userId, updatedUser);
    return updatedUser;
  }

  getUsersByRoom(roomId: string): User[] {
    return Array.from(this.users.values()).filter(user => user.roomId === roomId);
  }

  removeUserFromRoom(userId: string): void {
    const user = this.get(userId);
    if (user) {
      // Remove user from room members
      roomsManager.removeUserFromRoom(user.roomId, userId);
      // Delete user from users map
      this.delete(userId);
    }
  }

  getInactiveUsers(minutes: number): User[] {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    return Array.from(this.users.values()).filter(user => {
      const userCreated = new Date(user.created);
      return userCreated < cutoffTime;
    });
  }
}

// Helper function to check if request is from room host
export const requestIsNotFromHost = (
  socket: CustomSocket, 
  rooms: Map<string, any>
): boolean => {
  if (!socket.data?.roomId || !socket.data?.userId) return true;
  
  const room = rooms.get(socket.data.roomId);
  if (!room) return true;
  
  return room.host !== socket.data.userId;
};

// Helper function to check if user is admin
export const isUserAdmin = (socket: CustomSocket): boolean => {
  return socket.data?.isAdmin === true;
};

// Helper function to validate username
export const validateUsername = (username: string): { valid: boolean; error?: string } => {
  if (typeof username !== 'string') {
    return { valid: false, error: 'Username must be a string' };
  }
  
  if (username.length === 0) {
    return { valid: false, error: 'Username cannot be empty' };
  }
  
  if (username.length > 20) {
    return { valid: false, error: 'Username cannot exceed 20 characters' };
  }
  
  // Check for inappropriate content (basic filter)
  const inappropriateWords = ['admin', 'moderator', 'bot'];
  const lowerUsername = username.toLowerCase();
  
  for (const word of inappropriateWords) {
    if (lowerUsername.includes(word)) {
      return { valid: false, error: 'Username contains inappropriate content' };
    }
  }
  
  return { valid: true };
};

export const usersManager = new UserManager();
