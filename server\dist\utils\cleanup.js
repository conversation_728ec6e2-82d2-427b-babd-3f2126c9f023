"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.startCleanupInterval = startCleanupInterval;
exports.logStats = logStats;
const room_management_1 = require("./room-management");
const user_management_1 = require("./user-management");
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
const ROOM_TIMEOUT = 10 * 60; // 10 minutes
const USER_TIMEOUT = 30 * 60; // 30 minutes
function startCleanupInterval() {
    setInterval(() => {
        cleanupEmptyRooms();
        cleanupInactiveUsers();
    }, CLEANUP_INTERVAL);
    console.log(`🧹 Cleanup interval started (every ${CLEANUP_INTERVAL / 1000 / 60} minutes)`);
}
function cleanupEmptyRooms() {
    const emptyRooms = room_management_1.roomsManager.getRoomsOlderThan(ROOM_TIMEOUT);
    emptyRooms.forEach(room => {
        room_management_1.roomsManager.delete(room.id);
        console.log(`🧼 Cleanup: Deleted empty room ${room.id} (${room.name})`);
    });
    if (emptyRooms.length > 0) {
        console.log(`🧹 Cleaned up ${emptyRooms.length} empty rooms`);
    }
}
function cleanupInactiveUsers() {
    const inactiveUsers = user_management_1.usersManager.getInactiveUsers(USER_TIMEOUT);
    inactiveUsers.forEach(user => {
        // Remove user from their room
        room_management_1.roomsManager.removeUserFromRoom(user.roomId, user.id);
        // Remove user from users map
        user_management_1.usersManager.delete(user.id);
        console.log(`🧼 Cleanup: Removed inactive user ${user.id} (${user.username})`);
    });
    if (inactiveUsers.length > 0) {
        console.log(`🧹 Cleaned up ${inactiveUsers.length} inactive users`);
    }
}
function logStats() {
    const activeRooms = room_management_1.roomsManager.getActiveRooms();
    const totalUsers = user_management_1.usersManager.getLength();
    console.log(`📊 Stats: ${activeRooms.length} active rooms, ${totalUsers} connected users`);
}
//# sourceMappingURL=cleanup.js.map