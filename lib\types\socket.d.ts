import { Socket, Server } from "socket.io";
import { ChatMessage, Room, ServerMessage, User, GameQuestion, PlayerAnswer, GameResults } from "./multiplayer";
export declare const SOCKET_ACTIONS: {
    readonly CONNECT: "connect";
    readonly DISCONNECT: "disconnect";
    readonly CONNECT_ERROR: "connect_error";
    readonly CREATE_ROOM: "CREATE_ROOM";
    readonly JOIN_ROOM: "JOIN_ROOM";
    readonly JOIN_ROOM_BY_INVITE: "JOIN_ROOM_BY_INVITE";
    readonly LEAVE_ROOM: "LEAVE_ROOM";
    readonly CHECK_IF_ROOM_EXISTS: "CHECK_IF_ROOM_EXISTS";
    readonly GET_ROOM_INFO: "GET_ROOM_INFO";
    readonly RECONNECT_USER: "RECONNECT_USER";
    readonly SET_HOST: "SET_HOST";
    readonly SET_ADMIN: "SET_ADMIN";
    readonly KICK_USER: "KICK_USER";
    readonly GET_USER_INFO: "GET_USER_INFO";
    readonly USER_READY: "USER_READY";
    readonly USER_MESSAGE: "USER_MESSAGE";
    readonly SERVER_MESSAGE: "SERVER_MESSAGE";
    readonly START_GAME: "START_GAME";
    readonly END_GAME: "END_GAME";
    readonly PAUSE_GAME: "PAUSE_GAME";
    readonly RESUME_GAME: "RESUME_GAME";
    readonly NEW_QUESTION: "NEW_QUESTION";
    readonly SUBMIT_ANSWER: "SUBMIT_ANSWER";
    readonly QUESTION_TIMEOUT: "QUESTION_TIMEOUT";
    readonly SHOW_RESULTS: "SHOW_RESULTS";
    readonly NEXT_QUESTION: "NEXT_QUESTION";
    readonly GAME_RESULTS: "GAME_RESULTS";
    readonly CHANGE_SETTINGS: "CHANGE_SETTINGS";
};
export interface ClientToServerEvents {
    [SOCKET_ACTIONS.CONNECT]: () => void;
    [SOCKET_ACTIONS.DISCONNECT]: () => void;
    [SOCKET_ACTIONS.CREATE_ROOM]: (username: string, roomName: string, settings: Room["settings"], callback: (value: {
        result?: Room;
        error?: string;
    }) => void) => void;
    [SOCKET_ACTIONS.JOIN_ROOM]: (roomId: string, username: string, callback: (value: {
        success: boolean;
        error?: string;
    }) => void) => void;
    [SOCKET_ACTIONS.JOIN_ROOM_BY_INVITE]: (inviteCode: string, username: string, roomPasscode: string, callback: (value: {
        success: boolean;
        roomId?: string;
        error?: string;
    }) => void) => void;
    [SOCKET_ACTIONS.LEAVE_ROOM]: () => void;
    [SOCKET_ACTIONS.CHECK_IF_ROOM_EXISTS]: (roomId: string, callback: (room: Room | null) => void) => void;
    [SOCKET_ACTIONS.RECONNECT_USER]: (roomId: string, userId: string | null, callback: (value: {
        success: boolean;
        error?: string;
    }) => void) => void;
    [SOCKET_ACTIONS.SET_HOST]: (userId: string) => void;
    [SOCKET_ACTIONS.KICK_USER]: (userId: string) => void;
    [SOCKET_ACTIONS.USER_READY]: (isReady: boolean) => void;
    [SOCKET_ACTIONS.USER_MESSAGE]: (message: string, roomId: string) => void;
    [SOCKET_ACTIONS.START_GAME]: () => void;
    [SOCKET_ACTIONS.END_GAME]: () => void;
    [SOCKET_ACTIONS.PAUSE_GAME]: () => void;
    [SOCKET_ACTIONS.RESUME_GAME]: () => void;
    [SOCKET_ACTIONS.SUBMIT_ANSWER]: (answer: string) => void;
    [SOCKET_ACTIONS.NEXT_QUESTION]: () => void;
    [SOCKET_ACTIONS.CHANGE_SETTINGS]: (newSettings: Partial<Room["settings"]>) => void;
}
export interface ServerToClientEvents {
    [SOCKET_ACTIONS.CONNECT]: () => void;
    [SOCKET_ACTIONS.DISCONNECT]: () => void;
    [SOCKET_ACTIONS.CONNECT_ERROR]: (error: any) => void;
    [SOCKET_ACTIONS.SET_ADMIN]: () => void;
    [SOCKET_ACTIONS.LEAVE_ROOM]: () => void;
    [SOCKET_ACTIONS.KICK_USER]: () => void;
    [SOCKET_ACTIONS.GET_ROOM_INFO]: (room: Room) => void;
    [SOCKET_ACTIONS.GET_USER_INFO]: (user: User) => void;
    [SOCKET_ACTIONS.SERVER_MESSAGE]: (message: ServerMessage) => void;
    [SOCKET_ACTIONS.USER_MESSAGE]: (message: ChatMessage) => void;
    [SOCKET_ACTIONS.NEW_QUESTION]: (question: GameQuestion) => void;
    [SOCKET_ACTIONS.SHOW_RESULTS]: (answers: PlayerAnswer[]) => void;
    [SOCKET_ACTIONS.GAME_RESULTS]: (results: GameResults) => void;
    [SOCKET_ACTIONS.START_GAME]: () => void;
    [SOCKET_ACTIONS.END_GAME]: () => void;
    [SOCKET_ACTIONS.PAUSE_GAME]: () => void;
    [SOCKET_ACTIONS.RESUME_GAME]: () => void;
    [SOCKET_ACTIONS.QUESTION_TIMEOUT]: () => void;
}
export interface InterServerEvents {
    ping: () => void;
}
export interface SocketData {
    userId: string | undefined;
    roomId: string | undefined;
    isAdmin: boolean | undefined;
}
export type CustomSocket = Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
export type CustomServer = Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
//# sourceMappingURL=socket.d.ts.map