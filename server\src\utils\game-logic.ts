import { GameQuestion, PlayerAnswer, GameResults, Room } from '../../../lib/types/multiplayer';
import { Difficulty } from '../../../lib/constants';

// Import the countries data - we'll need to copy this or create a shared module
// For now, I'll create a simplified version
interface Country {
  name: string;
  code: string;
  flag: string;
  region: string;
  difficulty?: Difficulty;
}

// Simplified countries data - in a real implementation, you'd import from your shared data
const COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', flag: '🇺🇸', region: 'North America', difficulty: 'easy' },
  { name: 'United Kingdom', code: 'GB', flag: '🇬🇧', region: 'Europe', difficulty: 'easy' },
  { name: 'France', code: 'FR', flag: '🇫🇷', region: 'Europe', difficulty: 'easy' },
  { name: 'Germany', code: 'DE', flag: '🇩🇪', region: 'Europe', difficulty: 'easy' },
  { name: 'Japan', code: 'JP', flag: '🇯🇵', region: 'Asia', difficulty: 'easy' },
  { name: 'Brazil', code: 'BR', flag: '🇧🇷', region: 'South America', difficulty: 'easy' },
  { name: 'Canada', code: 'CA', flag: '🇨🇦', region: 'North America', difficulty: 'easy' },
  { name: 'Australia', code: 'AU', flag: '🇦🇺', region: 'Oceania', difficulty: 'easy' },
  { name: 'India', code: 'IN', flag: '🇮🇳', region: 'Asia', difficulty: 'medium' },
  { name: 'China', code: 'CN', flag: '🇨🇳', region: 'Asia', difficulty: 'medium' },
  { name: 'Russia', code: 'RU', flag: '🇷🇺', region: 'Europe/Asia', difficulty: 'medium' },
  { name: 'South Africa', code: 'ZA', flag: '🇿🇦', region: 'Africa', difficulty: 'medium' },
  { name: 'Mexico', code: 'MX', flag: '🇲🇽', region: 'North America', difficulty: 'medium' },
  { name: 'Argentina', code: 'AR', flag: '🇦🇷', region: 'South America', difficulty: 'medium' },
  { name: 'Netherlands', code: 'NL', flag: '🇳🇱', region: 'Europe', difficulty: 'hard' },
  { name: 'Switzerland', code: 'CH', flag: '🇨🇭', region: 'Europe', difficulty: 'hard' },
  { name: 'Sweden', code: 'SE', flag: '🇸🇪', region: 'Europe', difficulty: 'hard' },
  { name: 'Norway', code: 'NO', flag: '🇳🇴', region: 'Europe', difficulty: 'hard' },
  { name: 'Denmark', code: 'DK', flag: '🇩🇰', region: 'Europe', difficulty: 'hard' },
  { name: 'Finland', code: 'FI', flag: '🇫🇮', region: 'Europe', difficulty: 'hard' },
  { name: 'Estonia', code: 'EE', flag: '🇪🇪', region: 'Europe', difficulty: 'expert' },
  { name: 'Latvia', code: 'LV', flag: '🇱🇻', region: 'Europe', difficulty: 'expert' },
  { name: 'Lithuania', code: 'LT', flag: '🇱🇹', region: 'Europe', difficulty: 'expert' },
  { name: 'Slovenia', code: 'SI', flag: '🇸🇮', region: 'Europe', difficulty: 'expert' },
  { name: 'Slovakia', code: 'SK', flag: '🇸🇰', region: 'Europe', difficulty: 'expert' },
];

export class GameLogic {
  static getDifficultySettings(difficulty: Difficulty) {
    switch (difficulty) {
      case 'easy':
        return { count: 10, timePerQuestion: 30, optionsCount: 4 };
      case 'medium':
        return { count: 15, timePerQuestion: 25, optionsCount: 4 };
      case 'hard':
        return { count: 20, timePerQuestion: 20, optionsCount: 6 };
      case 'expert':
        return { count: 25, timePerQuestion: 15, optionsCount: 6 };
      default:
        return { count: 10, timePerQuestion: 30, optionsCount: 4 };
    }
  }

  static getCountriesByDifficulty(difficulty: Difficulty): Country[] {
    switch (difficulty) {
      case 'easy':
        return COUNTRIES.filter(c => c.difficulty === 'easy');
      case 'medium':
        return COUNTRIES.filter(c => c.difficulty === 'easy' || c.difficulty === 'medium');
      case 'hard':
        return COUNTRIES.filter(c => c.difficulty !== 'expert');
      case 'expert':
        return COUNTRIES;
      default:
        return COUNTRIES.filter(c => c.difficulty === 'easy');
    }
  }

  static generateQuestion(difficulty: Difficulty, questionNumber: number): GameQuestion {
    const settings = this.getDifficultySettings(difficulty);
    const availableCountries = this.getCountriesByDifficulty(difficulty);
    
    // Select a random country as the correct answer
    const correctCountry = availableCountries[Math.floor(Math.random() * availableCountries.length)];
    
    // Generate wrong options
    const wrongOptions: Country[] = [];
    const usedCountries = new Set([correctCountry.code]);
    
    while (wrongOptions.length < settings.optionsCount - 1) {
      const randomCountry = availableCountries[Math.floor(Math.random() * availableCountries.length)];
      if (!usedCountries.has(randomCountry.code)) {
        wrongOptions.push(randomCountry);
        usedCountries.add(randomCountry.code);
      }
    }
    
    // Combine and shuffle options
    const allOptions = [correctCountry, ...wrongOptions];
    for (let i = allOptions.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [allOptions[i], allOptions[j]] = [allOptions[j], allOptions[i]];
    }
    
    return {
      currentCountry: correctCountry,
      options: allOptions,
      questionNumber,
      timeLimit: settings.timePerQuestion,
    };
  }

  static calculateScore(isCorrect: boolean, timeToAnswer: number, timeLimit: number): number {
    if (!isCorrect) return 0;
    
    // Base points for correct answer
    const basePoints = 100;
    
    // Time bonus (faster answers get more points)
    const timeBonus = Math.max(0, Math.floor((timeLimit - timeToAnswer) / timeLimit * 50));
    
    return basePoints + timeBonus;
  }

  static processAnswer(
    answer: string, 
    correctAnswer: string, 
    timeToAnswer: number, 
    timeLimit: number,
    userId: string
  ): PlayerAnswer {
    const isCorrect = answer.toLowerCase() === correctAnswer.toLowerCase();
    const points = this.calculateScore(isCorrect, timeToAnswer, timeLimit);
    
    return {
      userId,
      answer,
      isCorrect,
      timeToAnswer,
      points,
    };
  }

  static calculateFinalResults(room: Room): GameResults {
    const userScores = new Map<string, {
      username: string;
      score: number;
      correctAnswers: number;
      totalTime: number;
      answerCount: number;
    }>();

    // Initialize user scores
    room.members.forEach(member => {
      userScores.set(member.id, {
        username: member.username,
        score: member.score,
        correctAnswers: 0,
        totalTime: 0,
        answerCount: 0,
      });
    });

    // Process all answers from the game
    // Note: In a real implementation, you'd store all answers throughout the game
    // For now, we'll use the current member scores
    
    const finalScores = Array.from(userScores.entries()).map(([userId, data]) => ({
      userId,
      username: data.username,
      score: data.score,
      correctAnswers: data.correctAnswers,
      averageTime: data.answerCount > 0 ? data.totalTime / data.answerCount : 0,
    }));

    // Sort by score (highest first)
    finalScores.sort((a, b) => b.score - a.score);

    return {
      finalScores,
      totalQuestions: room.gameState.totalQuestions,
      difficulty: room.gameState.difficulty,
      gameEndTime: new Date().toISOString(),
    };
  }

  static isGameComplete(room: Room): boolean {
    return room.gameState.currentQuestionIndex >= room.gameState.totalQuestions;
  }

  static areAllPlayersReady(room: Room): boolean {
    return room.members.length > 0 && room.members.every(member => member.isReady);
  }

  static getNextQuestionIndex(room: Room): number {
    return room.gameState.currentQuestionIndex + 1;
  }
}
