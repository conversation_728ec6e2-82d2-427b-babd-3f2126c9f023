"use client";

import React from 'react';
import { useSocket } from '@/lib/context/SocketContext';
import { SOCKET_ACTIONS } from '@/lib/types/socket';
import { Room, User } from '@/lib/types/multiplayer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Copy, Crown, UserMinus, ArrowLeft } from 'lucide-react';

interface RoomLobbyProps {
  room: Room;
  user: User;
  onLeaveRoom: () => void;
}

export default function RoomLobby({ room, user, onLeaveRoom }: RoomLobbyProps) {
  const { socket } = useSocket();
  const isHost = user.id === room.host;
  const allPlayersReady = room.members.every(member => member.isReady);
  const canStartGame = room.members.length >= 2 && allPlayersReady;

  const handleToggleReady = () => {
    if (socket) {
      socket.emit(SOCKET_ACTIONS.TOGGLE_READY);
    }
  };

  const handleStartGame = () => {
    if (socket && isHost && canStartGame) {
      socket.emit(SOCKET_ACTIONS.START_GAME);
    }
  };

  const handleKickUser = (userId: string) => {
    if (socket && isHost) {
      socket.emit(SOCKET_ACTIONS.KICK_USER, { userId });
    }
  };

  const copyRoomCode = () => {
    navigator.clipboard.writeText(room.code);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={onLeaveRoom}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Leave Room</span>
          </Button>
        </div>
        
        <div className="text-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-2">
            {room.name}
          </h1>
          <div className="flex items-center justify-center gap-2 mb-4">
            <span className="text-muted-foreground">Room Code:</span>
            <Badge variant="secondary" className="text-lg font-mono">
              {room.code}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={copyRoomCode}
              className="h-6 w-6 p-0"
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-muted-foreground text-sm">
            Share this code with friends to invite them
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Players List */}
        <Card className="shadow-card">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">
              Players ({room.members.length}/{room.maxRoomSize})
            </h2>
            
            <div className="space-y-3">
              {room.members.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm"
                      style={{ backgroundColor: member.color }}
                    >
                      {member.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-foreground">{member.username}</span>
                        {member.id === room.host && <Crown className="h-4 w-4 text-yellow-500" />}
                        {member.id === user.id && <span className="text-xs text-muted-foreground">(You)</span>}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Score: {member.score} • {member.isReady ? '✅ Ready' : '⏳ Not Ready'}
                      </div>
                    </div>
                  </div>

                  {isHost && member.id !== user.id && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleKickUser(member.id)}
                      className="text-red-500 hover:text-red-600 h-8 w-8 p-0"
                    >
                      <UserMinus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Game Settings */}
        <Card className="shadow-card">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold text-foreground mb-4">Game Settings</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Difficulty</span>
                <Badge variant="outline" className="capitalize">
                  {room.settings.difficulty}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Questions</span>
                <Badge variant="outline">
                  {room.settings.questionsCount}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Time per Question</span>
                <Badge variant="outline">
                  {room.settings.timePerQuestion}s
                </Badge>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              {/* Ready Button */}
              <Button
                onClick={handleToggleReady}
                variant={user.isReady ? "secondary" : "default"}
                className="w-full"
                size="lg"
              >
                {user.isReady ? '✅ Ready' : '⏳ Not Ready'}
              </Button>

              {/* Start Game Button (Host Only) */}
              {isHost && (
                <Button
                  onClick={handleStartGame}
                  disabled={!canStartGame}
                  className="w-full"
                  size="lg"
                >
                  {canStartGame ? '🚀 Start Game' : 'Waiting for all players to be ready...'}
                </Button>
              )}

              {!isHost && (
                <div className="text-center text-sm text-muted-foreground">
                  Waiting for host to start the game...
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
