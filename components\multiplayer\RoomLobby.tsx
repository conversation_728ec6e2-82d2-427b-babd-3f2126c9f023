"use client";

import React, { useState } from 'react';
import { useSocket } from '@/lib/context/SocketContext';
import { SOCKET_ACTIONS } from '@/lib/types/socket';
import { Room, User, ChatMessage, ServerMessage } from '@/lib/types/multiplayer';
import { Difficulty } from '@/lib/constants';
import Chat from './Chat';

interface RoomLobbyProps {
  room: Room;
  user: User;
  messages: (ChatMessage | ServerMessage)[];
  onSendMessage: (message: string) => void;
  onLeaveRoom: () => void;
}

export default function RoomLobby({ room, user, messages, onSendMessage, onLeaveRoom }: RoomLobbyProps) {
  const { socket } = useSocket();
  const [isReady, setIsReady] = useState(user.isReady);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState(room.settings);

  const isHost = user.id === room.host;
  const allPlayersReady = room.members.every(member => member.isReady);
  const canStartGame = room.members.length >= 1 && allPlayersReady;

  const handleReadyToggle = () => {
    const newReadyState = !isReady;
    setIsReady(newReadyState);
    if (socket) {
      socket.emit(SOCKET_ACTIONS.USER_READY, newReadyState);
    }
  };

  const handleStartGame = () => {
    if (socket && isHost && canStartGame) {
      socket.emit(SOCKET_ACTIONS.START_GAME);
    }
  };

  const handleKickUser = (userId: string) => {
    if (socket && isHost && userId !== user.id) {
      socket.emit(SOCKET_ACTIONS.KICK_USER, userId);
    }
  };

  const handleSetHost = (userId: string) => {
    if (socket && isHost && userId !== user.id) {
      socket.emit(SOCKET_ACTIONS.SET_HOST, userId);
    }
  };

  const handleUpdateSettings = () => {
    if (socket && isHost) {
      socket.emit(SOCKET_ACTIONS.CHANGE_SETTINGS, settings);
      setShowSettings(false);
    }
  };

  const copyInviteCode = () => {
    navigator.clipboard.writeText(room.inviteCode);
  };

  const copyRoomId = () => {
    navigator.clipboard.writeText(room.id);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">{room.name}</h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>Room ID: <code className="bg-gray-100 px-2 py-1 rounded font-mono">{room.id}</code></span>
                <button onClick={copyRoomId} className="text-blue-500 hover:text-blue-600">📋 Copy</button>
                <span>Invite: <code className="bg-gray-100 px-2 py-1 rounded font-mono">{room.inviteCode}</code></span>
                <button onClick={copyInviteCode} className="text-blue-500 hover:text-blue-600">📋 Copy</button>
              </div>
            </div>
            <button
              onClick={onLeaveRoom}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Leave Room
            </button>
          </div>

          {/* Game Settings */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-semibold text-gray-800">Game Settings</h3>
              {isHost && (
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="text-blue-500 hover:text-blue-600 text-sm"
                >
                  ⚙️ Edit
                </button>
              )}
            </div>
            
            {showSettings && isHost ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Difficulty</label>
                    <select
                      value={settings.difficulty}
                      onChange={(e) => setSettings({ ...settings, difficulty: e.target.value as Difficulty })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    >
                      <option value="easy">Easy</option>
                      <option value="medium">Medium</option>
                      <option value="hard">Hard</option>
                      <option value="expert">Expert</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Questions</label>
                    <select
                      value={settings.questionCount}
                      onChange={(e) => setSettings({ ...settings, questionCount: parseInt(e.target.value) })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    >
                      <option value={5}>5</option>
                      <option value={10}>10</option>
                      <option value={15}>15</option>
                      <option value={20}>20</option>
                      <option value={25}>25</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Time/Q</label>
                    <select
                      value={settings.timePerQuestion}
                      onChange={(e) => setSettings({ ...settings, timePerQuestion: parseInt(e.target.value) })}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                    >
                      <option value={10}>10s</option>
                      <option value={15}>15s</option>
                      <option value={20}>20s</option>
                      <option value={30}>30s</option>
                      <option value={45}>45s</option>
                      <option value={60}>60s</option>
                    </select>
                  </div>
                  <div className="flex items-end">
                    <button
                      onClick={handleUpdateSettings}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white text-sm px-2 py-1 rounded transition-colors"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Difficulty:</span>
                  <span className="ml-2 font-medium capitalize">{room.settings.difficulty}</span>
                </div>
                <div>
                  <span className="text-gray-600">Questions:</span>
                  <span className="ml-2 font-medium">{room.settings.questionCount}</span>
                </div>
                <div>
                  <span className="text-gray-600">Time per Question:</span>
                  <span className="ml-2 font-medium">{room.settings.timePerQuestion}s</span>
                </div>
                <div>
                  <span className="text-gray-600">Spectators:</span>
                  <span className="ml-2 font-medium">{room.settings.allowSpectators ? 'Allowed' : 'Not allowed'}</span>
                </div>
              </div>
            )}
          </div>

          {/* Ready/Start Game Section */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleReadyToggle}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isReady
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              {isReady ? '✅ Ready' : '⏳ Not Ready'}
            </button>

            {isHost && (
              <button
                onClick={handleStartGame}
                disabled={!canStartGame}
                className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                  canStartGame
                    ? 'bg-blue-500 hover:bg-blue-600 text-white'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                🚀 Start Game
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Players List */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">
                Players ({room.members.length}/{room.maxRoomSize})
              </h2>
              
              <div className="space-y-3">
                {room.members.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium"
                        style={{ backgroundColor: member.color }}
                      >
                        {member.username.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{member.username}</span>
                          {member.id === room.host && <span className="text-yellow-500">👑</span>}
                          {member.isAdmin && <span className="text-red-500">🛡️</span>}
                        </div>
                        <div className="text-sm text-gray-600">
                          Score: {member.score} • {member.isReady ? '✅ Ready' : '⏳ Not Ready'}
                        </div>
                      </div>
                    </div>

                    {isHost && member.id !== user.id && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleSetHost(member.id)}
                          className="text-yellow-500 hover:text-yellow-600 text-sm"
                          title="Make Host"
                        >
                          👑
                        </button>
                        <button
                          onClick={() => handleKickUser(member.id)}
                          className="text-red-500 hover:text-red-600 text-sm"
                          title="Kick Player"
                        >
                          🚫
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Chat */}
          <div className="lg:col-span-1">
            <Chat messages={messages} onSendMessage={onSendMessage} />
          </div>
        </div>
      </div>
    </div>
  );
}
