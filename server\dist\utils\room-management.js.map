{"version": 3, "file": "room-management.js", "sourceRoot": "", "sources": ["../../src/utils/room-management.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAIhC,MAAM,WAAW;IAAjB;QACS,UAAK,GAAG,IAAI,GAAG,EAAgB,CAAC;IAsKzC,CAAC;IApKC,MAAM,CACJ,MAAc,EACd,QAAgB,EAChB,IAAU,EACV,QAAoC;QAEpC,MAAM,eAAe,GAAG;YACtB,UAAU,EAAE,MAAoB;YAChC,aAAa,EAAE,EAAE;YACjB,eAAe,EAAE,EAAE;YACnB,eAAe,EAAE,IAAI;SACtB,CAAC;QAEF,MAAM,SAAS,GAAc;YAC3B,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,IAAI;YACrB,iBAAiB,EAAE,IAAI;YACvB,OAAO,EAAE,EAAE;YACX,oBAAoB,EAAE,CAAC;YACvB,cAAc,EAAE,QAAQ,EAAE,aAAa,IAAI,eAAe,CAAC,aAAa;YACxE,UAAU,EAAE,QAAQ,EAAE,UAAU,IAAI,eAAe,CAAC,UAAU;YAC9D,WAAW,EAAE,IAAI;SAClB,CAAC;QAEF,MAAM,IAAI,GAAS;YACjB,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,IAAI,CAAC,EAAE;YACb,UAAU,EAAE,IAAA,eAAM,EAAC,CAAC,CAAC,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,IAAI;YACd,SAAS;YACT,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,0BAA0B,EAAE,EAAE;YAC9B,WAAW,EAAE,CAAC,EAAE,0CAA0C;YAC1D,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACjC,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,QAAQ,EAAE;SAC9C,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG,CAAC,MAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,GAAG,CAAC,MAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,GAAG,CAAC,MAAc,EAAE,IAAU;QAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,MAAc;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,OAAsB;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,WAAW,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC9B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,mBAAmB,CAAC,UAAkB;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,0BAA0B,CAAC,MAAc,EAAE,MAAc;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO,SAAS,CAAC;QAE5B,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IAClF,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,IAAU;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,wBAAwB;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,uCAAuC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACxE,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,CAAC,uBAAuB;QACtC,CAAC;QAED,mBAAmB;QACnB,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;QAErE,2DAA2D;QAC3D,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAC7D,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,CACpC,CAAC;QAEF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,MAAM,0BAA0B,GAAG;gBACjC,GAAG,IAAI,CAAC,0BAA0B;gBAClC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;aAC7C,CAAC;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,0BAA0B,EAAE,0BAA0B,EAAE,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,kBAAkB,CAAC,MAAc,EAAE,MAAc;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED,UAAU,CAAC,MAAc,EAAE,SAAiB;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,8CAA8C;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,eAAe,CAAC,MAAc,EAAE,gBAAoC;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,MAAM,gBAAgB,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,gBAAgB,EAAE,CAAC;QACpE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED,cAAc;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC9D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YACnD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,OAAO,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAEY,QAAA,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC"}