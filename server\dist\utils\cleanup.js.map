{"version": 3, "file": "cleanup.js", "sourceRoot": "", "sources": ["../../src/utils/cleanup.ts"], "names": [], "mappings": ";;AAOA,oDAOC;AA+BD,4BAKC;AAlDD,uDAAiD;AACjD,uDAAiD;AAEjD,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;AACpD,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa;AAC3C,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa;AAE3C,SAAgB,oBAAoB;IAClC,WAAW,CAAC,GAAG,EAAE;QACf,iBAAiB,EAAE,CAAC;QACpB,oBAAoB,EAAE,CAAC;IACzB,CAAC,EAAE,gBAAgB,CAAC,CAAC;IAErB,OAAO,CAAC,GAAG,CAAC,sCAAsC,gBAAgB,GAAG,IAAI,GAAG,EAAE,WAAW,CAAC,CAAC;AAC7F,CAAC;AAED,SAAS,iBAAiB;IACxB,MAAM,UAAU,GAAG,8BAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAEhE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACxB,8BAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,iBAAiB,UAAU,CAAC,MAAM,cAAc,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB;IAC3B,MAAM,aAAa,GAAG,8BAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAElE,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC3B,8BAA8B;QAC9B,8BAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACtD,6BAA6B;QAC7B,8BAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,qCAAqC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACjF,CAAC,CAAC,CAAC;IAEH,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,iBAAiB,aAAa,CAAC,MAAM,iBAAiB,CAAC,CAAC;IACtE,CAAC;AACH,CAAC;AAED,SAAgB,QAAQ;IACtB,MAAM,WAAW,GAAG,8BAAY,CAAC,cAAc,EAAE,CAAC;IAClD,MAAM,UAAU,GAAG,8BAAY,CAAC,SAAS,EAAE,CAAC;IAE5C,OAAO,CAAC,GAAG,CAAC,aAAa,WAAW,CAAC,MAAM,kBAAkB,UAAU,kBAAkB,CAAC,CAAC;AAC7F,CAAC"}