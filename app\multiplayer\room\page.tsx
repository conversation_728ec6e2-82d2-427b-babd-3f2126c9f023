"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import SocketProvider from '@/lib/context/SocketProvider';
import MultiplayerRoom from '@/components/multiplayer/MultiplayerRoom';

export default function RoomPage() {
  const router = useRouter();
  const [sessionToken, setSessionToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get session token from localStorage
    const token = localStorage.getItem('sessionToken');
    const username = localStorage.getItem('username');
    const roomAction = localStorage.getItem('roomAction');

    if (!token || !username || !roomAction) {
      // Redirect to lobby if missing required data
      router.push('/multiplayer');
      return;
    }

    setSessionToken(token);
    setIsLoading(false);
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading multiplayer room...</p>
        </div>
      </div>
    );
  }

  if (!sessionToken) {
    return null; // Will redirect in useEffect
  }

  return (
    <SocketProvider sessionToken={sessionToken}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <MultiplayerRoom />
      </div>
    </SocketProvider>
  );
}
