// Server-side type definitions
export type UserId = string;
export type RoomId = string;
export type Difficulty = 'easy' | 'medium' | 'hard' | 'expert';

export interface Country {
  name: string;
  code: string;
  flag: string;
  region: string;
  difficulty?: Difficulty;
}

export interface User {
  id: UserId;
  socketId: string;
  username: string;
  roomId: RoomId;
  created: string;
  color: string;
  isAdmin?: boolean;
  score: number;
  isReady: boolean;
}

export interface GameQuestion {
  currentCountry: Country;
  options: Country[];
  questionNumber: number;
  timeLimit: number;
}

export interface PlayerAnswer {
  userId: UserId;
  answer: string;
  isCorrect: boolean;
  timeToAnswer: number;
  points: number;
}

export interface GameState {
  status: 'lobby' | 'playing' | 'finished';
  isActive: boolean;
  isPaused: boolean;
  currentQuestion: GameQuestion | null;
  currentQuestionIndex: number;
  questionStartTime: number | null;
  startTime?: number;
  endTime?: number;
  answers: PlayerAnswer[];
  totalQuestions: number;
  difficulty: Difficulty;
  gameEndTime: number | null;
  currentCountry?: Country;
}

export interface RoomSettings {
  difficulty: Difficulty;
  questionsCount: number;
  timePerQuestion: number;
  maxRoomSize: number;
  allowSpectators?: boolean;
  questionCount?: number;
}

export interface Room {
  id: string;
  name: string;
  host: string;
  inviteCode: string | null;
  code: string;
  passcode: string | null;
  gameState: GameState;
  members: User[];
  previouslyConnectedMembers: User[];
  maxRoomSize: number;
  created: string;
  private: boolean;
  settings: RoomSettings;
}

export interface GameResults {
  finalScores: Array<{
    userId: string;
    username: string;
    score: number;
    rank: number;
  }>;
  totalQuestions: number;
  difficulty: Difficulty;
  gameEndTime: number;
}

export interface ChatMessage {
  id: string;
  userId: string;
  username: string;
  message: string;
  timestamp: number;
  type: 'user' | 'system';
}

export interface ServerMessage {
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: number;
}

// Socket event interfaces
export interface ClientToServerEvents {
  CREATE_ROOM: (username: string, roomName: string, settings: RoomSettings, callback?: (response: any) => void) => void;
  JOIN_ROOM: (roomId: string, username: string, callback?: (response: any) => void) => void;
  JOIN_ROOM_BY_INVITE: (inviteCode: string, username: string, roomPasscode?: string, callback?: (response: any) => void) => void;
  LEAVE_ROOM: () => void;
  CHECK_IF_ROOM_EXISTS: (roomId: string, callback: (room: Room | null) => void) => void;
  GET_ROOM_INFO: (roomId: string, callback: (room: Room | null) => void) => void;
  RECONNECT_USER: (sessionToken: string, callback?: (response: any) => void) => void;
  SET_HOST: (userId: string) => void;
  SET_ADMIN: () => void;
  KICK_USER: (data: { userId: string }) => void;
  GET_USER_INFO: (userId: string, callback: (user: User | null) => void) => void;
  TOGGLE_READY: () => void;
  USER_MESSAGE: (message: string) => void;
  START_GAME: () => void;
  END_GAME: () => void;
  PAUSE_GAME: () => void;
  RESUME_GAME: () => void;
  NEW_QUESTION: (question: GameQuestion) => void;
  SUBMIT_ANSWER: (data: { answer: string; timeRemaining: number }) => void;
  QUESTION_TIMEOUT: () => void;
  SHOW_RESULTS: (results: GameResults) => void;
  NEXT_QUESTION: () => void;
}

export interface ServerToClientEvents {
  ROOM_UPDATE: (room: Room) => void;
  USER_UPDATE: (user: User) => void;
  GAME_STARTED: () => void;
  GAME_RESULTS: (results: GameResults) => void;
  NEXT_QUESTION: () => void;
  ERROR: (message: string) => void;
  SET_ADMIN: () => void;
  USER_MESSAGE: (message: ChatMessage) => void;
  SERVER_MESSAGE: (message: ServerMessage) => void;
  NEW_QUESTION: (question: GameQuestion) => void;
  QUESTION_TIMEOUT: () => void;
  SHOW_RESULTS: (results: GameResults) => void;
}

export interface InterServerEvents {}

export interface SocketData {
  userId: string;
  roomId?: string;
  isAdmin?: boolean;
}
