import { nanoid } from 'nanoid';
import { Room, User, GameState } from '../../../lib/types/multiplayer';
import { Difficulty } from '../../../lib/constants';

class RoomManager {
  public rooms = new Map<string, Room>();

  create(
    roomId: string, 
    roomName: string, 
    host: User, 
    settings?: Partial<Room['settings']>
  ): Room {
    const defaultSettings = {
      difficulty: 'easy' as Difficulty,
      questionCount: 10,
      timePerQuestion: 30,
      allowSpectators: true,
    };

    const gameState: GameState = {
      isActive: false,
      isPaused: false,
      currentQuestion: null,
      questionStartTime: null,
      answers: [],
      currentQuestionIndex: 0,
      totalQuestions: settings?.questionCount || defaultSettings.questionCount,
      difficulty: settings?.difficulty || defaultSettings.difficulty,
      gameEndTime: null,
    };

    const room: Room = {
      id: roomId,
      name: roomName,
      host: host.id,
      inviteCode: nanoid(5).toUpperCase(),
      passcode: null,
      gameState,
      members: [host],
      previouslyConnectedMembers: [],
      maxRoomSize: 8, // Default max room size for flag guessing
      created: new Date().toISOString(),
      private: false,
      settings: { ...defaultSettings, ...settings },
    };

    this.rooms.set(roomId, room);
    return room;
  }

  get(roomId: string): Room | undefined {
    return this.rooms.get(roomId);
  }

  has(roomId: string): boolean {
    return this.rooms.has(roomId);
  }

  set(roomId: string, room: Room): void {
    this.rooms.set(roomId, room);
  }

  delete(roomId: string): boolean {
    return this.rooms.delete(roomId);
  }

  update(roomId: string, updates: Partial<Room>): Room {
    const room = this.get(roomId);
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }

    const updatedRoom = { ...room, ...updates };
    this.set(roomId, updatedRoom);
    return updatedRoom;
  }

  getRoomByInviteCode(inviteCode: string): Room | undefined {
    for (const room of this.rooms.values()) {
      if (room.inviteCode === inviteCode) {
        return room;
      }
    }
    return undefined;
  }

  getPreviouslyConnectedUser(userId: string, roomId: string): { userId: string; username: string } | undefined {
    const room = this.get(roomId);
    if (!room) return undefined;

    return room.previouslyConnectedMembers.find(member => member.userId === userId);
  }

  addUserToRoom(roomId: string, user: User): Room | null {
    const room = this.get(roomId);
    if (!room) return null;

    // Check if room is full
    if (room.members.length >= room.maxRoomSize) {
      return null;
    }

    // Check if user is already in the room
    const existingUser = room.members.find(member => member.id === user.id);
    if (existingUser) {
      return room; // User already in room
    }

    // Add user to room
    const updatedMembers = [...room.members, user];
    const updatedRoom = this.update(roomId, { members: updatedMembers });

    // Add to previously connected members if not already there
    const wasConnectedBefore = room.previouslyConnectedMembers.find(
      member => member.userId === user.id
    );
    
    if (!wasConnectedBefore) {
      const updatedPreviouslyConnected = [
        ...room.previouslyConnectedMembers,
        { userId: user.id, username: user.username }
      ];
      return this.update(roomId, { previouslyConnectedMembers: updatedPreviouslyConnected });
    }

    return updatedRoom;
  }

  removeUserFromRoom(roomId: string, userId: string): Room | null {
    const room = this.get(roomId);
    if (!room) return null;

    const updatedMembers = room.members.filter(member => member.id !== userId);
    return this.update(roomId, { members: updatedMembers });
  }

  setNewHost(roomId: string, newHostId: string): Room | null {
    const room = this.get(roomId);
    if (!room) return null;

    // Verify the new host is a member of the room
    const newHost = room.members.find(member => member.id === newHostId);
    if (!newHost) return null;

    return this.update(roomId, { host: newHostId });
  }

  updateGameState(roomId: string, gameStateUpdates: Partial<GameState>): Room | null {
    const room = this.get(roomId);
    if (!room) return null;

    const updatedGameState = { ...room.gameState, ...gameStateUpdates };
    return this.update(roomId, { gameState: updatedGameState });
  }

  getActiveRooms(): Room[] {
    return Array.from(this.rooms.values()).filter(room => room.members.length > 0);
  }

  getEmptyRooms(): Room[] {
    return Array.from(this.rooms.values()).filter(room => room.members.length === 0);
  }

  getRoomsOlderThan(minutes: number): Room[] {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    return Array.from(this.rooms.values()).filter(room => {
      const roomCreated = new Date(room.created);
      return roomCreated < cutoffTime && room.members.length === 0;
    });
  }
}

export const roomsManager = new RoomManager();
