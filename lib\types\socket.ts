import { Socket, Server } from "socket.io";
import { 
  ChatMessage, 
  Room, 
  ServerMessage, 
  User, 
  GameQuestion,
  PlayerAnswer,
  GameResults
} from "./multiplayer";

// Socket action constants
export const SOCKET_ACTIONS = {
  // Connection
  CONNECT: "connect",
  DISCONNECT: "disconnect",
  CONNECT_ERROR: "connect_error",
  
  // Room management
  CREATE_ROOM: "CREATE_ROOM",
  JOIN_ROOM: "JOIN_ROOM",
  JOIN_ROOM_BY_INVITE: "JOIN_ROOM_BY_INVITE",
  LEAVE_ROOM: "LEAVE_ROOM",
  CHECK_IF_ROOM_EXISTS: "CHECK_IF_ROOM_EXISTS",
  GET_ROOM_INFO: "GET_ROOM_INFO",
  RECONNECT_USER: "RECONNECT_USER",
  
  // User management
  SET_HOST: "SET_HOST",
  SET_ADMIN: "SET_ADMIN",
  <PERSON><PERSON><PERSON>_USER: "KICK_USER",
  GET_USER_INFO: "GET_USER_INFO",
  USER_READY: "USER_READY",
  
  // Chat
  USER_MESSAGE: "USER_MESSAGE",
  SERVER_MESSAGE: "SERVER_MESSAGE",
  
  // Game flow
  START_GAME: "START_GAME",
  END_GAME: "END_GAME",
  PAUSE_GAME: "PAUSE_GAME",
  RESUME_GAME: "RESUME_GAME",
  
  // Questions and answers
  NEW_QUESTION: "NEW_QUESTION",
  SUBMIT_ANSWER: "SUBMIT_ANSWER",
  QUESTION_TIMEOUT: "QUESTION_TIMEOUT",
  SHOW_RESULTS: "SHOW_RESULTS",
  NEXT_QUESTION: "NEXT_QUESTION",
  
  // Game results
  GAME_RESULTS: "GAME_RESULTS",
  
  // Settings
  CHANGE_SETTINGS: "CHANGE_SETTINGS",
} as const;

export interface ClientToServerEvents {
  [SOCKET_ACTIONS.CONNECT]: () => void;
  [SOCKET_ACTIONS.DISCONNECT]: () => void;
  
  [SOCKET_ACTIONS.CREATE_ROOM]: (
    username: string,
    roomName: string,
    settings: Room["settings"],
    callback: (value: { result?: Room; error?: string }) => void
  ) => void;
  
  [SOCKET_ACTIONS.JOIN_ROOM]: (
    roomId: string,
    username: string,
    callback: (value: { success: boolean; error?: string }) => void
  ) => void;
  
  [SOCKET_ACTIONS.JOIN_ROOM_BY_INVITE]: (
    inviteCode: string,
    username: string,
    roomPasscode: string,
    callback: (value: { success: boolean; roomId?: string; error?: string }) => void
  ) => void;
  
  [SOCKET_ACTIONS.LEAVE_ROOM]: () => void;
  
  [SOCKET_ACTIONS.CHECK_IF_ROOM_EXISTS]: (
    roomId: string,
    callback: (room: Room | null) => void
  ) => void;
  
  [SOCKET_ACTIONS.RECONNECT_USER]: (
    roomId: string,
    userId: string | null,
    callback: (value: { success: boolean; error?: string }) => void
  ) => void;
  
  [SOCKET_ACTIONS.SET_HOST]: (userId: string) => void;
  [SOCKET_ACTIONS.KICK_USER]: (userId: string) => void;
  [SOCKET_ACTIONS.USER_READY]: (isReady: boolean) => void;
  
  [SOCKET_ACTIONS.USER_MESSAGE]: (message: string, roomId: string) => void;
  
  [SOCKET_ACTIONS.START_GAME]: () => void;
  [SOCKET_ACTIONS.END_GAME]: () => void;
  [SOCKET_ACTIONS.PAUSE_GAME]: () => void;
  [SOCKET_ACTIONS.RESUME_GAME]: () => void;
  
  [SOCKET_ACTIONS.SUBMIT_ANSWER]: (answer: string) => void;
  [SOCKET_ACTIONS.NEXT_QUESTION]: () => void;
  
  [SOCKET_ACTIONS.CHANGE_SETTINGS]: (newSettings: Partial<Room["settings"]>) => void;
}

export interface ServerToClientEvents {
  [SOCKET_ACTIONS.CONNECT]: () => void;
  [SOCKET_ACTIONS.DISCONNECT]: () => void;
  [SOCKET_ACTIONS.CONNECT_ERROR]: (error: any) => void;
  
  [SOCKET_ACTIONS.SET_ADMIN]: () => void;
  [SOCKET_ACTIONS.LEAVE_ROOM]: () => void;
  [SOCKET_ACTIONS.KICK_USER]: () => void;
  
  [SOCKET_ACTIONS.GET_ROOM_INFO]: (room: Room) => void;
  [SOCKET_ACTIONS.GET_USER_INFO]: (user: User) => void;
  
  [SOCKET_ACTIONS.SERVER_MESSAGE]: (message: ServerMessage) => void;
  [SOCKET_ACTIONS.USER_MESSAGE]: (message: ChatMessage) => void;
  
  [SOCKET_ACTIONS.NEW_QUESTION]: (question: GameQuestion) => void;
  [SOCKET_ACTIONS.SHOW_RESULTS]: (answers: PlayerAnswer[]) => void;
  [SOCKET_ACTIONS.GAME_RESULTS]: (results: GameResults) => void;
  
  [SOCKET_ACTIONS.START_GAME]: () => void;
  [SOCKET_ACTIONS.END_GAME]: () => void;
  [SOCKET_ACTIONS.PAUSE_GAME]: () => void;
  [SOCKET_ACTIONS.RESUME_GAME]: () => void;
  
  [SOCKET_ACTIONS.QUESTION_TIMEOUT]: () => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  userId: string | undefined;
  roomId: string | undefined;
  isAdmin: boolean | undefined;
}

export type CustomSocket = Socket<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>;

export type CustomServer = Server<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>;
