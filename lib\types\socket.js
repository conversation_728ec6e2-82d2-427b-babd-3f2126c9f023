"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SOCKET_ACTIONS = void 0;
// Socket action constants
exports.SOCKET_ACTIONS = {
    // Connection
    CONNECT: "connect",
    DISCONNECT: "disconnect",
    CONNECT_ERROR: "connect_error",
    // Room management
    CREATE_ROOM: "CREATE_ROOM",
    JOIN_ROOM: "JOIN_ROOM",
    JOIN_ROOM_BY_INVITE: "JOIN_ROOM_BY_INVITE",
    LEAVE_ROOM: "LEAVE_ROOM",
    CHECK_IF_ROOM_EXISTS: "CHECK_IF_ROOM_EXISTS",
    GET_ROOM_INFO: "GET_ROOM_INFO",
    RECONNECT_USER: "RECONNECT_USER",
    // User management
    SET_HOST: "SET_HOST",
    SET_ADMIN: "SET_ADMIN",
    KICK_USER: "KICK_USER",
    GET_USER_INFO: "GET_USER_INFO",
    USER_READY: "USER_READY",
    // Chat
    USER_MESSAGE: "USER_MESSAGE",
    SERVER_MESSAGE: "SERVER_MESSAGE",
    // Game flow
    START_GAME: "START_GAME",
    END_GAME: "END_GAME",
    PAUSE_GAME: "PAUSE_GAME",
    RESUME_GAME: "RESUME_GAME",
    // Questions and answers
    NEW_QUESTION: "NEW_QUESTION",
    SUBMIT_ANSWER: "SUBMIT_ANSWER",
    QUESTION_TIMEOUT: "QUESTION_TIMEOUT",
    SHOW_RESULTS: "SHOW_RESULTS",
    NEXT_QUESTION: "NEXT_QUESTION",
    // Game results
    GAME_RESULTS: "GAME_RESULTS",
    // Settings
    CHANGE_SETTINGS: "CHANGE_SETTINGS",
};
//# sourceMappingURL=socket.js.map