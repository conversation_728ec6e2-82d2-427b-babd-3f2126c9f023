import express, { Application } from 'express';
import { createServer, Server as HttpServer } from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import { config } from 'dotenv';
import {
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
} from '../../lib/types/socket';
import { roomsManager } from './utils/room-management';
import { usersManager } from './utils/user-management';
import { handleSocketEvents } from './handlers/socket-events';
import { startCleanupInterval } from './utils/cleanup';

config();

const PORT = (process.env.PORT && parseInt(process.env.PORT)) || 3001;
const app: Application = express();
const server: HttpServer = createServer(app);

const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'https://flags.games',
  'https://www.flags.games',
  // Add your production domains here
];

const corsOptions = {
  origin: allowedOrigins,
  methods: ['GET', 'POST'],
  credentials: true,
};

app.use(cors(corsOptions));
app.use(express.json());

const io = new Server<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>(server, {
  cors: corsOptions,
  allowRequest: (req, callback) => {
    const isAllowedOrigin = req?.headers?.origin 
      ? allowedOrigins.includes(req.headers.origin) 
      : true; // Allow requests without origin (like from Postman)
    callback(null, isAllowedOrigin);
  },
});

// Socket.io middleware for authentication
io.use((socket, next) => {
  const userId = socket.handshake.auth.token;
  if (userId) {
    next();
  } else {
    next(new Error('Missing session token'));
  }
});

// Handle socket connections
io.on('connection', (socket) => {
  handleSocketEvents(io, socket);
});

// Start cleanup interval for inactive rooms
startCleanupInterval();

// Health check endpoint
app.get('/api/healthz', (_req, res) => {
  res.send({ status: 'ok', timestamp: new Date().toISOString() });
});

// Get all rooms (for debugging/admin)
app.get('/api/rooms', (_req, res) => {
  res.json({ 
    rooms: Object.fromEntries(roomsManager.rooms.entries()),
    count: roomsManager.rooms.size
  });
});

// Get specific room
app.get('/api/room/:roomId', (req, res) => {
  const roomId = req.params?.roomId as string;
  const room = roomsManager.get(roomId) || null;
  res.json({ room });
});

// Get room by invite code
app.get('/api/room-invite/:inviteCode', (req, res) => {
  const inviteCode = req.params?.inviteCode as string;
  const room = roomsManager.getRoomByInviteCode(inviteCode) || null;
  res.json({ room });
});

// Get all users (for debugging/admin)
app.get('/api/users', (_req, res) => {
  res.json({ 
    users: Object.fromEntries(usersManager.users.entries()),
    count: usersManager.users.size
  });
});

// Get connection stats
app.get('/api/stats', (_req, res) => {
  const activeConnections = io.sockets.sockets.size;
  const roomIds = Array.from(roomsManager.rooms).map((room) => room[0]);
  const userIds = Array.from(usersManager.users).map((user) => user[0]);
  
  res.json({
    activeConnections,
    users: {
      ids: userIds,
      count: userIds.length,
    },
    rooms: {
      ids: roomIds,
      count: roomIds.length,
    },
    timestamp: new Date().toISOString(),
  });
});

server.listen(PORT, () => {
  console.log(`🚀 Flag Guessing Game WebSocket server is running on http://localhost:${PORT}`);
  console.log(`🌍 Allowed origins: ${allowedOrigins.join(', ')}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
