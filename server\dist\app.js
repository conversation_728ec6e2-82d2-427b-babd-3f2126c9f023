"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const cors_1 = __importDefault(require("cors"));
const socket_io_1 = require("socket.io");
const dotenv_1 = require("dotenv");
const room_management_1 = require("./utils/room-management");
const user_management_1 = require("./utils/user-management");
const socket_events_1 = require("./handlers/socket-events");
const cleanup_1 = require("./utils/cleanup");
(0, dotenv_1.config)();
const PORT = (process.env.PORT && parseInt(process.env.PORT)) || 3001;
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://flags.games',
    'https://www.flags.games',
    // Add your production domains here
];
const corsOptions = {
    origin: allowedOrigins,
    methods: ['GET', 'POST'],
    credentials: true,
};
app.use((0, cors_1.default)(corsOptions));
app.use(express_1.default.json());
const io = new socket_io_1.Server(server, {
    cors: corsOptions,
    allowRequest: (req, callback) => {
        const isAllowedOrigin = req?.headers?.origin
            ? allowedOrigins.includes(req.headers.origin)
            : true; // Allow requests without origin (like from Postman)
        callback(null, isAllowedOrigin);
    },
});
// Socket.io middleware for authentication
io.use((socket, next) => {
    const userId = socket.handshake.auth.token;
    if (userId) {
        next();
    }
    else {
        next(new Error('Missing session token'));
    }
});
// Handle socket connections
io.on('connection', (socket) => {
    (0, socket_events_1.handleSocketEvents)(io, socket);
});
// Start cleanup interval for inactive rooms
(0, cleanup_1.startCleanupInterval)();
// Health check endpoint
app.get('/api/healthz', (_req, res) => {
    res.send({ status: 'ok', timestamp: new Date().toISOString() });
});
// Get all rooms (for debugging/admin)
app.get('/api/rooms', (_req, res) => {
    res.json({
        rooms: Object.fromEntries(room_management_1.roomsManager.rooms.entries()),
        count: room_management_1.roomsManager.rooms.size
    });
});
// Get specific room
app.get('/api/room/:roomId', (req, res) => {
    const roomId = req.params?.roomId;
    const room = room_management_1.roomsManager.get(roomId) || null;
    res.json({ room });
});
// Get room by invite code
app.get('/api/room-invite/:inviteCode', (req, res) => {
    const inviteCode = req.params?.inviteCode;
    const room = room_management_1.roomsManager.getRoomByInviteCode(inviteCode) || null;
    res.json({ room });
});
// Get all users (for debugging/admin)
app.get('/api/users', (_req, res) => {
    res.json({
        users: Object.fromEntries(user_management_1.usersManager.users.entries()),
        count: user_management_1.usersManager.users.size
    });
});
// Get connection stats
app.get('/api/stats', (_req, res) => {
    const activeConnections = io.sockets.sockets.size;
    const roomIds = Array.from(room_management_1.roomsManager.rooms).map((room) => room[0]);
    const userIds = Array.from(user_management_1.usersManager.users).map((user) => user[0]);
    res.json({
        activeConnections,
        users: {
            ids: userIds,
            count: userIds.length,
        },
        rooms: {
            ids: roomIds,
            count: roomIds.length,
        },
        timestamp: new Date().toISOString(),
    });
});
server.listen(PORT, () => {
    console.log(`🚀 Flag Guessing Game WebSocket server is running on http://localhost:${PORT}`);
    console.log(`🌍 Allowed origins: ${allowedOrigins.join(', ')}`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
//# sourceMappingURL=app.js.map