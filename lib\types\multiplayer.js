"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerMessageType = void 0;
var ServerMessageType;
(function (ServerMessageType) {
    ServerMessageType["ALERT"] = "ALERT";
    ServerMessageType["DEFAULT"] = "DEFAULT";
    ServerMessageType["UPDATE"] = "UPDATE";
    ServerMessageType["NEW_HOST"] = "NEW_HOST";
    ServerMessageType["ERROR"] = "ERROR";
    ServerMessageType["USER_JOINED"] = "USER_JOINED";
    ServerMessageType["USER_DISCONNECTED"] = "USER_DISCONNECTED";
    ServerMessageType["USER_RECONNECTED"] = "USER_RECONNECTED";
    ServerMessageType["GAME_STARTED"] = "GAME_STARTED";
    ServerMessageType["GAME_ENDED"] = "GAME_ENDED";
    ServerMessageType["QUESTION_STARTED"] = "QUESTION_STARTED";
    ServerMessageType["QUESTION_ENDED"] = "QUESTION_ENDED";
    ServerMessageType["PLAYER_ANSWERED"] = "PLAYER_ANSWERED";
})(ServerMessageType || (exports.ServerMessageType = ServerMessageType = {}));
//# sourceMappingURL=multiplayer.js.map