# Multiplayer Flag Guessing Game

This document explains how to run the multiplayer version of the flag guessing game.

## Architecture

The multiplayer functionality consists of two parts:
- **Next.js Frontend** (port 3000) - The main web application
- **WebSocket Server** (port 3001) - Handles real-time multiplayer communication

## Quick Start

### Option 1: Use the Batch Script (Windows)
Double-click `start-multiplayer.bat` or run:
```bash
start-multiplayer.bat
```
This opens two terminal windows - one for each service.

### Option 2: Use PowerShell Script (Windows)
```powershell
powershell -ExecutionPolicy Bypass -File start-multiplayer.ps1
```

### Option 3: Manual Start (All Platforms)

#### 1. Start the WebSocket Server (Terminal 1)
```bash
npm run server:dev
```
The server will start on `http://localhost:3001`

#### 2. Start the Next.js App (Terminal 2)
```bash
npm run dev
```
The app will start on `http://localhost:3000`

## Available Scripts

### Development
- `npm run dev` - Start Next.js app only
- `npm run server:dev` - Start WebSocket server only
- `npm run dev:multiplayer` - Shows instructions for running both
- `start-multiplayer.bat` - Windows batch script to start both (opens 2 terminals)
- `start-multiplayer.sh` - Unix/Linux shell script to start both

### Production
- `npm run build:full` - Build both app and server
- `npm run start:full` - Start both in production mode
- `npm run server:build` - Build server only
- `npm run server:start` - Start server in production mode

## Server Features

The WebSocket server provides:
- ✅ Real-time room management
- ✅ User authentication and session handling
- ✅ Game state synchronization
- ✅ Automatic cleanup of inactive rooms
- ✅ RESTful API endpoints for debugging

### API Endpoints
- `GET /api/healthz` - Health check
- `GET /api/stats` - Connection statistics
- `GET /api/rooms` - List all active rooms
- `GET /api/users` - List all connected users

## Multiplayer Flow

1. **Create/Join Room** - Players create or join game rooms using invite codes
2. **Lobby** - Players wait in lobby, toggle ready status
3. **Game Start** - Host starts game when all players are ready
4. **Real-time Gameplay** - Synchronized questions and scoring
5. **Results** - Final scores and rankings

## Development Notes

- The server runs independently and can be developed/tested separately
- CORS is configured to allow connections from `localhost:3000`
- Socket.io handles reconnection and error recovery automatically
- Room cleanup happens every 5 minutes for inactive rooms

## Troubleshooting

### Server Won't Start
- Check if port 3001 is available
- Ensure all dependencies are installed: `cd server && npm install`

### Connection Issues
- Verify both server and app are running
- Check browser console for WebSocket errors
- Ensure firewall isn't blocking port 3001

### Type Errors
- The server has its own TypeScript configuration
- Run `npm run server:build` to check for server-side type errors
