{"version": 3, "file": "socket.d.ts", "sourceRoot": "", "sources": ["socket.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAC3C,OAAO,EACL,WAAW,EACX,IAAI,EACJ,aAAa,EACb,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,WAAW,EACZ,MAAM,eAAe,CAAC;AAGvB,eAAO,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4CjB,CAAC;AAEX,MAAM,WAAW,oBAAoB;IACnC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;IACrC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IAExC,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAC5B,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,EAC1B,QAAQ,EAAE,CAAC,KAAK,EAAE;QAAE,MAAM,CAAC,EAAE,IAAI,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,KACzD,IAAI,CAAC;IAEV,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAC1B,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,KAC5D,IAAI,CAAC;IAEV,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAAE,CACpC,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,KAC7E,IAAI,CAAC;IAEV,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IAExC,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,CACrC,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,KAAK,IAAI,KAClC,IAAI,CAAC;IAEV,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAC/B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,GAAG,IAAI,EACrB,QAAQ,EAAE,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,KAC5D,IAAI,CAAC;IAEV,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACpD,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACrD,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IAExD,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IAEzE,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;IACtC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,IAAI,CAAC;IAEzC,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IACzD,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,MAAM,IAAI,CAAC;IAE3C,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,CAAC;CACpF;AAED,MAAM,WAAW,oBAAoB;IACnC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC;IACrC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,CAAC;IAErD,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;IACvC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;IAEvC,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;IACrD,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;IAErD,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK,IAAI,CAAC;IAClE,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,KAAK,IAAI,CAAC;IAE9D,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,KAAK,IAAI,CAAC;IAChE,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,IAAI,CAAC;IACjE,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,KAAK,IAAI,CAAC;IAE9D,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;IACtC,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC;IACxC,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,IAAI,CAAC;IAEzC,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,MAAM,IAAI,CAAC;CAC/C;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,IAAI,CAAC;CAClB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,OAAO,EAAE,OAAO,GAAG,SAAS,CAAC;CAC9B;AAED,MAAM,MAAM,YAAY,GAAG,MAAM,CAC/B,oBAAoB,EACpB,oBAAoB,EACpB,iBAAiB,EACjB,UAAU,CACX,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,MAAM,CAC/B,oBAAoB,EACpB,oBAAoB,EACpB,iBAAiB,EACjB,UAAU,CACX,CAAC"}