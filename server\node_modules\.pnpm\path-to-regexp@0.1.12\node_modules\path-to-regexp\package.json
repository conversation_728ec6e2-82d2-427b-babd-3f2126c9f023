{"name": "path-to-regexp", "description": "Express style path to RegExp utility", "version": "0.1.12", "files": ["index.js", "LICENSE"], "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "keywords": ["express", "regexp"], "component": {"scripts": {"path-to-regexp": "index.js"}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pillarjs/path-to-regexp.git"}, "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}