hoistPattern:
  - '*'
hoistedDependencies:
  /@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  /@socket.io/component-emitter/3.1.2:
    '@socket.io/component-emitter': private
  /@types/body-parser/1.19.6:
    '@types/body-parser': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/express-serve-static-core/4.19.6:
    '@types/express-serve-static-core': private
  /@types/http-errors/2.0.5:
    '@types/http-errors': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/send/0.17.5:
    '@types/send': private
  /@types/serve-static/1.15.8:
    '@types/serve-static': private
  /accepts/1.3.8:
    accepts: private
  /array-flatten/1.1.1:
    array-flatten: private
  /base64id/2.0.0:
    base64id: private
  /body-parser/1.20.3:
    body-parser: private
  /bytes/3.1.2:
    bytes: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bound/1.0.4:
    call-bound: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.7.1:
    cookie: private
  /debug/2.6.9:
    debug: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /ee-first/1.1.1:
    ee-first: private
  /encodeurl/2.0.0:
    encodeurl: private
  /engine.io-parser/5.2.3:
    engine.io-parser: private
  /engine.io/6.6.4:
    engine.io: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /esbuild/0.25.5:
    esbuild: private
  /escape-html/1.0.3:
    escape-html: private
  /etag/1.8.1:
    etag: private
  /finalhandler/1.3.1:
    finalhandler: private
  /forwarded/0.2.0:
    forwarded: private
  /fresh/0.5.2:
    fresh: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /gopd/1.2.0:
    gopd: private
  /has-symbols/1.1.0:
    has-symbols: private
  /hasown/2.0.2:
    hasown: private
  /http-errors/2.0.0:
    http-errors: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /inherits/2.0.4:
    inherits: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /media-typer/0.3.0:
    media-typer: private
  /merge-descriptors/1.0.3:
    merge-descriptors: private
  /methods/1.1.2:
    methods: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /ms/2.0.0:
    ms: private
  /negotiator/0.6.3:
    negotiator: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /on-finished/2.4.1:
    on-finished: private
  /parseurl/1.3.3:
    parseurl: private
  /path-to-regexp/0.1.12:
    path-to-regexp: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /qs/6.13.0:
    qs: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /send/0.19.0:
    send: private
  /serve-static/1.16.2:
    serve-static: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /socket.io-adapter/2.5.5:
    socket.io-adapter: private
  /socket.io-parser/4.2.4:
    socket.io-parser: private
  /statuses/2.0.1:
    statuses: private
  /toidentifier/1.0.1:
    toidentifier: private
  /type-is/1.6.18:
    type-is: private
  /undici-types/6.21.0:
    undici-types: private
  /unpipe/1.0.0:
    unpipe: private
  /utils-merge/1.0.1:
    utils-merge: private
  /vary/1.1.2:
    vary: private
  /ws/8.17.1:
    ws: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.1
pendingBuilds: []
prunedAt: Sun, 06 Jul 2025 22:07:10 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-arm64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /fsevents/2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Projects\_Next.js\flags.games\server\node_modules\.pnpm
