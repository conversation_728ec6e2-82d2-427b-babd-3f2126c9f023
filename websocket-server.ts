import { createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { nanoid } from 'nanoid';
import { 
  GameRoom, 
  Player, 
  SOCKET_EVENTS, 
  CreateRoomPayload, 
  JoinRoomPayload, 
  SubmitAnswerPayload,
  SendMessagePayload,
  UpdateSettingsPayload,
  SocketResponse,
  MultiplayerGameState,
  RoomSettings,
  MessageType,
  ChatMessage,
  PlayerAnswer
} from './lib/types/multiplayer';
import { generateQuestion, getDifficultySettings } from './lib/utils/gameLogic';
import { DEFAULT_DIFFICULTY, CORRECT_POINT_COST } from './lib/constants';

// Server state
const rooms = new Map<string, GameRoom>();
const players = new Map<string, Player>();
const playerSockets = new Map<string, WebSocket>();

// Utility functions
const generateRoomId = () => nanoid(6).toUpperCase();
const generateInviteCode = () => nanoid(8).toUpperCase();
const generatePlayerId = () => nanoid();

// Default room settings
const DEFAULT_ROOM_SETTINGS: RoomSettings = {
  difficulty: DEFAULT_DIFFICULTY,
  questionCount: 10,
  questionTimeLimit: 30,
  allowSpectators: true,
  autoStart: false,
  showLeaderboard: true
};

// Create HTTP server for health checks
const server = createServer((req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'ok', 
      rooms: rooms.size, 
      players: players.size,
      timestamp: new Date().toISOString()
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

// Create WebSocket server
const wss = new WebSocketServer({ server });

// Helper functions
const sendToPlayer = (playerId: string, event: string, data: any) => {
  const socket = playerSockets.get(playerId);
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({ event, data }));
  }
};

const sendToRoom = (roomId: string, event: string, data: any, excludePlayerId?: string) => {
  const room = rooms.get(roomId);
  if (!room) return;
  
  room.players.forEach(player => {
    if (player.id !== excludePlayerId) {
      sendToPlayer(player.id, event, data);
    }
  });
};

const createDefaultGameState = (settings: RoomSettings): MultiplayerGameState => ({
  currentQuestion: 0,
  totalQuestions: settings.questionCount,
  currentCountry: null,
  options: [],
  difficulty: settings.difficulty,
  gameStarted: false,
  gameCompleted: false,
  questionStartTime: 0,
  questionTimeLimit: settings.questionTimeLimit,
  usedCountries: new Set(),
  showResults: false,
  correctAnswer: null
});

const createChatMessage = (type: MessageType, content: string, playerId?: string, playerName?: string): ChatMessage => ({
  id: nanoid(),
  type,
  content,
  playerId,
  playerName,
  timestamp: new Date()
});

// Room management functions
const createRoom = (payload: CreateRoomPayload, playerId: string): SocketResponse<{ room: GameRoom; player: Player }> => {
  try {
    const roomId = generateRoomId();
    const inviteCode = generateInviteCode();
    const now = new Date();
    
    const settings: RoomSettings = {
      ...DEFAULT_ROOM_SETTINGS,
      ...payload.settings
    };

    const player: Player = {
      id: playerId,
      username: payload.playerName,
      score: 0,
      isHost: true,
      isReady: false,
      socketId: playerId,
      joinedAt: now,
      lastActivity: now
    };

    const room: GameRoom = {
      id: roomId,
      name: payload.roomName,
      host: playerId,
      players: [player],
      gameState: createDefaultGameState(settings),
      settings,
      createdAt: now,
      lastActivity: now,
      inviteCode,
      isPrivate: payload.isPrivate || false,
      maxPlayers: 8,
      playerAnswers: {}
    };

    rooms.set(roomId, room);
    players.set(playerId, player);

    console.log(`🏠 Room created: ${roomId} by ${payload.playerName}`);
    
    return {
      success: true,
      data: { room, player }
    };
  } catch (error) {
    console.error('Error creating room:', error);
    return {
      success: false,
      error: 'Failed to create room'
    };
  }
};

const joinRoom = (payload: JoinRoomPayload, playerId: string): SocketResponse<{ room: GameRoom; player: Player }> => {
  try {
    const room = rooms.get(payload.roomId);
    if (!room) {
      return {
        success: false,
        error: 'Room not found'
      };
    }

    if (room.players.length >= room.maxPlayers) {
      return {
        success: false,
        error: 'Room is full'
      };
    }

    if (room.gameState.gameStarted && !room.settings.allowSpectators) {
      return {
        success: false,
        error: 'Game already started and spectators not allowed'
      };
    }

    // Check if player already exists in room
    const existingPlayer = room.players.find(p => p.id === playerId);
    if (existingPlayer) {
      // Reconnecting player
      existingPlayer.lastActivity = new Date();
      players.set(playerId, existingPlayer);
      
      return {
        success: true,
        data: { room, player: existingPlayer }
      };
    }

    const now = new Date();
    const player: Player = {
      id: playerId,
      username: payload.playerName,
      score: 0,
      isHost: false,
      isReady: false,
      socketId: playerId,
      joinedAt: now,
      lastActivity: now
    };

    room.players.push(player);
    room.lastActivity = now;
    players.set(playerId, player);

    console.log(`👤 Player joined: ${payload.playerName} -> Room ${payload.roomId}`);

    // Notify other players
    sendToRoom(payload.roomId, SOCKET_EVENTS.PLAYER_JOINED, { player }, playerId);
    sendToRoom(payload.roomId, SOCKET_EVENTS.ROOM_UPDATED, { room });

    return {
      success: true,
      data: { room, player }
    };
  } catch (error) {
    console.error('Error joining room:', error);
    return {
      success: false,
      error: 'Failed to join room'
    };
  }
};

// Game logic functions
const startGame = (roomId: string): boolean => {
  const room = rooms.get(roomId);
  if (!room || room.gameState.gameStarted) return false;

  // Reset game state
  room.gameState = {
    ...createDefaultGameState(room.settings),
    gameStarted: true
  };

  // Reset player scores
  room.players.forEach(player => {
    player.score = 0;
    player.isReady = false;
  });

  room.playerAnswers = {};

  // Generate first question
  const questionData = generateQuestion(room.settings.difficulty, room.gameState.usedCountries);
  if (!questionData) return false;

  room.gameState.currentQuestion = 1;
  room.gameState.currentCountry = questionData.currentCountry;
  room.gameState.options = questionData.options;
  room.gameState.questionStartTime = Date.now();
  room.gameState.usedCountries.add(questionData.currentCountry.code);

  console.log(`🎮 Game started in room ${roomId}`);

  // Notify all players
  sendToRoom(roomId, SOCKET_EVENTS.GAME_STARTED, { gameState: room.gameState });
  sendToRoom(roomId, SOCKET_EVENTS.QUESTION_STARTED, {
    question: room.gameState.currentQuestion,
    country: room.gameState.currentCountry,
    options: room.gameState.options,
    timeLimit: room.gameState.questionTimeLimit
  });

  return true;
};

const submitAnswer = (roomId: string, playerId: string, payload: SubmitAnswerPayload): boolean => {
  const room = rooms.get(roomId);
  const player = players.get(playerId);

  if (!room || !player || !room.gameState.gameStarted || room.gameState.gameCompleted) {
    return false;
  }

  if (payload.questionNumber !== room.gameState.currentQuestion) {
    return false; // Wrong question number
  }

  // Check if player already answered this question
  const questionAnswers = room.playerAnswers[payload.questionNumber] || [];
  if (questionAnswers.find(answer => answer.playerId === playerId)) {
    return false; // Already answered
  }

  const isCorrect = payload.selectedCountry === room.gameState.currentCountry?.code;
  const points = isCorrect ? CORRECT_POINT_COST : 0;

  const answer: PlayerAnswer = {
    playerId,
    questionNumber: payload.questionNumber,
    selectedCountry: payload.selectedCountry,
    isCorrect,
    timeToAnswer: payload.timeToAnswer,
    points
  };

  // Store answer
  if (!room.playerAnswers[payload.questionNumber]) {
    room.playerAnswers[payload.questionNumber] = [];
  }
  room.playerAnswers[payload.questionNumber].push(answer);

  // Update player score
  player.score += points;

  console.log(`📝 Answer submitted: ${player.username} -> ${isCorrect ? 'Correct' : 'Wrong'}`);

  // Notify room about the answer
  sendToRoom(roomId, SOCKET_EVENTS.PLAYER_ANSWERED, {
    playerId,
    playerName: player.username,
    isCorrect,
    timeToAnswer: payload.timeToAnswer
  });

  // Check if all players have answered
  const allAnswered = room.players.every(p =>
    room.playerAnswers[payload.questionNumber]?.find(a => a.playerId === p.id)
  );

  if (allAnswered) {
    endQuestion(roomId);
  }

  return true;
};

const endQuestion = (roomId: string) => {
  const room = rooms.get(roomId);
  if (!room) return;

  room.gameState.showResults = true;
  room.gameState.correctAnswer = room.gameState.currentCountry?.code || null;

  // Send question results
  sendToRoom(roomId, SOCKET_EVENTS.QUESTION_ENDED, {
    correctAnswer: room.gameState.correctAnswer,
    playerAnswers: room.playerAnswers[room.gameState.currentQuestion] || [],
    leaderboard: room.players.map(p => ({
      playerId: p.id,
      username: p.username,
      score: p.score
    })).sort((a, b) => b.score - a.score)
  });

  // Check if game is complete
  if (room.gameState.currentQuestion >= room.gameState.totalQuestions) {
    endGame(roomId);
  } else {
    // Move to next question after delay
    setTimeout(() => {
      nextQuestion(roomId);
    }, 3000);
  }
};

const nextQuestion = (roomId: string) => {
  const room = rooms.get(roomId);
  if (!room || room.gameState.gameCompleted) return;

  const questionData = generateQuestion(room.settings.difficulty, room.gameState.usedCountries);
  if (!questionData) {
    endGame(roomId);
    return;
  }

  room.gameState.currentQuestion++;
  room.gameState.currentCountry = questionData.currentCountry;
  room.gameState.options = questionData.options;
  room.gameState.questionStartTime = Date.now();
  room.gameState.showResults = false;
  room.gameState.correctAnswer = null;
  room.gameState.usedCountries.add(questionData.currentCountry.code);

  sendToRoom(roomId, SOCKET_EVENTS.QUESTION_STARTED, {
    question: room.gameState.currentQuestion,
    country: room.gameState.currentCountry,
    options: room.gameState.options,
    timeLimit: room.gameState.questionTimeLimit
  });
};

const endGame = (roomId: string) => {
  const room = rooms.get(roomId);
  if (!room) return;

  room.gameState.gameCompleted = true;
  room.gameState.gameStarted = false;

  const finalLeaderboard = room.players
    .map(p => ({
      playerId: p.id,
      username: p.username,
      score: p.score,
      correctAnswers: Object.values(room.playerAnswers)
        .flat()
        .filter(a => a.playerId === p.id && a.isCorrect).length
    }))
    .sort((a, b) => b.score - a.score);

  console.log(`🏁 Game ended in room ${roomId}`);

  sendToRoom(roomId, SOCKET_EVENTS.GAME_ENDED, {
    leaderboard: finalLeaderboard,
    gameStats: {
      totalQuestions: room.gameState.totalQuestions,
      difficulty: room.gameState.difficulty
    }
  });
};

const PORT = process.env.WS_PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 WebSocket server listening on port ${PORT}`);
  console.log(`📊 Health check available at http://localhost:${PORT}/health`);
});
