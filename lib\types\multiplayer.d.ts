import { Country } from "@/lib/data/countries";
import { Difficulty } from "@/lib/constants";
export type UserId = string;
export type RoomId = string;
export interface User {
    id: UserId;
    socketId: string;
    username: string;
    roomId: RoomId;
    created: string;
    color: string;
    isAdmin?: boolean;
    score: number;
    isReady: boolean;
}
export interface GameQuestion {
    currentCountry: Country;
    options: Country[];
    questionNumber: number;
    timeLimit: number;
}
export interface PlayerAnswer {
    userId: UserId;
    answer: string;
    isCorrect: boolean;
    timeToAnswer: number;
    points: number;
}
export interface GameState {
    isActive: boolean;
    isPaused: boolean;
    currentQuestion: GameQuestion | null;
    questionStartTime: number | null;
    answers: PlayerAnswer[];
    currentQuestionIndex: number;
    totalQuestions: number;
    difficulty: Difficulty;
    gameEndTime: number | null;
}
export interface Room {
    id: string;
    name: string;
    host: string;
    inviteCode: string | null;
    passcode: string | null;
    gameState: GameState;
    members: User[];
    previouslyConnectedMembers: {
        userId: UserId;
        username: string;
    }[];
    maxRoomSize: number;
    created: string;
    private: boolean;
    settings: {
        difficulty: Difficulty;
        questionCount: number;
        timePerQuestion: number;
        allowSpectators: boolean;
    };
}
export type Rooms = {
    [roomId: RoomId]: Room;
};
export interface ChatMessage {
    username: string;
    message: string;
    id: string;
    userId: UserId;
    timestamp: string;
    color: string;
    type: "USER";
    isAdmin: boolean;
}
export declare enum ServerMessageType {
    ALERT = "ALERT",
    DEFAULT = "DEFAULT",
    UPDATE = "UPDATE",
    NEW_HOST = "NEW_HOST",
    ERROR = "ERROR",
    USER_JOINED = "USER_JOINED",
    USER_DISCONNECTED = "USER_DISCONNECTED",
    USER_RECONNECTED = "USER_RECONNECTED",
    GAME_STARTED = "GAME_STARTED",
    GAME_ENDED = "GAME_ENDED",
    QUESTION_STARTED = "QUESTION_STARTED",
    QUESTION_ENDED = "QUESTION_ENDED",
    PLAYER_ANSWERED = "PLAYER_ANSWERED"
}
export interface ServerMessage {
    message: string;
    type: ServerMessageType;
    timestamp: string;
}
export type Message = ChatMessage | ServerMessage;
export type Messages = Message[];
export interface GameResults {
    finalScores: Array<{
        userId: UserId;
        username: string;
        score: number;
        correctAnswers: number;
        averageTime: number;
    }>;
    totalQuestions: number;
    difficulty: Difficulty;
    gameEndTime: string;
}
//# sourceMappingURL=multiplayer.d.ts.map