import { roomsManager } from './room-management';
import { usersManager } from './user-management';

const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
const ROOM_TIMEOUT = 10 * 60; // 10 minutes
const USER_TIMEOUT = 30 * 60; // 30 minutes

export function startCleanupInterval(): void {
  setInterval(() => {
    cleanupEmptyRooms();
    cleanupInactiveUsers();
  }, CLEANUP_INTERVAL);

  console.log(`🧹 Cleanup interval started (every ${CLEANUP_INTERVAL / 1000 / 60} minutes)`);
}

function cleanupEmptyRooms(): void {
  const emptyRooms = roomsManager.getRoomsOlderThan(ROOM_TIMEOUT);
  
  emptyRooms.forEach(room => {
    roomsManager.delete(room.id);
    console.log(`🧼 Cleanup: Deleted empty room ${room.id} (${room.name})`);
  });

  if (emptyRooms.length > 0) {
    console.log(`🧹 Cleaned up ${emptyRooms.length} empty rooms`);
  }
}

function cleanupInactiveUsers(): void {
  const inactiveUsers = usersManager.getInactiveUsers(USER_TIMEOUT);
  
  inactiveUsers.forEach(user => {
    // Remove user from their room
    roomsManager.removeUserFromRoom(user.roomId, user.id);
    // Remove user from users map
    usersManager.delete(user.id);
    console.log(`🧼 Cleanup: Removed inactive user ${user.id} (${user.username})`);
  });

  if (inactiveUsers.length > 0) {
    console.log(`🧹 Cleaned up ${inactiveUsers.length} inactive users`);
  }
}

export function logStats(): void {
  const activeRooms = roomsManager.getActiveRooms();
  const totalUsers = usersManager.getLength();
  
  console.log(`📊 Stats: ${activeRooms.length} active rooms, ${totalUsers} connected users`);
}
