"use client";

import React from "react";
import io from "socket.io-client";
import { useRouter } from "next/navigation";
import { SocketContext } from "./SocketContext";
import { CustomSocket } from "@/lib/types/socket";
import { Room, User } from "@/lib/types/multiplayer";
import { SOCKET_ACTIONS } from "@/lib/types/socket";

const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:3001";

interface SocketProviderProps {
  children: React.ReactNode;
  sessionToken: string | null;
  adminToken?: string;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({
  children,
  sessionToken,
  adminToken,
}) => {
  const [socket, setSocket] = React.useState<CustomSocket | null>(null);
  const [room, setRoom] = React.useState<Room | null | undefined>(undefined);
  const [user, setUser] = React.useState<User | null>(null);
  const [isConnecting, setIsConnecting] = React.useState(true);
  const [retries, setRetries] = React.useState(3);
  const router = useRouter();

  React.useEffect(() => {
    if (!sessionToken && typeof window !== "undefined") {
      if (retries > 0) {
        setRetries((prevRetries) => prevRetries - 1);
        // Generate a new session token if none exists
        const newToken = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        // You might want to store this in localStorage or handle it differently
        console.log("Generated new session token:", newToken);
      }
      return;
    }

    const newSocket = io(SOCKET_URL, {
      transports: ["websocket"],
      auth: {
        token: sessionToken,
        adminToken,
      },
    }) as unknown as CustomSocket;

    // Initialize socket data
    newSocket.data = {
      userId: '',
      roomId: '',
      isAdmin: false,
    };

    if (sessionToken) {
      newSocket.data.userId = sessionToken;
      newSocket.data.roomId = room?.id || '';
      newSocket.data.isAdmin = room?.host === newSocket.data.userId;
    }

    setSocket(newSocket);

    // Connection event handlers
    newSocket.on("connect_error", (err) => {
      console.log(`connect_error due to ${err.message}`);
      setIsConnecting(false);
    });

    newSocket.on("connect", () => {
      console.log("Connected to multiplayer server");
      setIsConnecting(false);
    });

    newSocket.on("disconnect", () => {
      console.log("Disconnected from multiplayer server");
      setIsConnecting(false);
      setRoom(null);
    });

    // Game-specific event handlers
    newSocket.on(SOCKET_ACTIONS.SET_ADMIN, () => {
      if (newSocket.data) {
        newSocket.data.isAdmin = true;
      }
    });

    newSocket.on(SOCKET_ACTIONS.GET_ROOM_INFO, (newRoom) => {
      setRoom(newRoom);
      if (newSocket.data) {
        newSocket.data.roomId = newRoom?.id || '';
        newSocket.data.isAdmin = newRoom?.host === newSocket.data.userId;
      }
    });

    newSocket.on(SOCKET_ACTIONS.GET_USER_INFO, (newUser) => {
      setUser(newUser);
    });

    // Cleanup function
    return () => {
      newSocket.disconnect();
    };
  }, [sessionToken, adminToken, room?.id, room?.host, retries]);

  return (
    <SocketContext.Provider
      value={{
        socket,
        sessionToken,
        room,
        user,
        isConnecting,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export default SocketProvider;
