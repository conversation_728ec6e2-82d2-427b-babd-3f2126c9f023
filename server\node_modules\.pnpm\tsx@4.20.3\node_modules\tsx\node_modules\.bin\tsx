#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/dist/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/dist/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules/tsx/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/tsx@4.20.3/node_modules:/mnt/c/Users/<USER>/Projects/_Next.js/flags.games/server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cli.mjs" "$@"
else
  exec node  "$basedir/../../dist/cli.mjs" "$@"
fi
