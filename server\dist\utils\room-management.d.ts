import { Room, User, GameState } from '../../../lib/types/multiplayer';
declare class RoomManager {
    rooms: Map<string, Room>;
    create(roomId: string, roomName: string, host: User, settings?: Partial<Room['settings']>): Room;
    get(roomId: string): Room | undefined;
    has(roomId: string): boolean;
    set(roomId: string, room: Room): void;
    delete(roomId: string): boolean;
    update(roomId: string, updates: Partial<Room>): Room;
    getRoomByInviteCode(inviteCode: string): Room | undefined;
    getPreviouslyConnectedUser(userId: string, roomId: string): {
        userId: string;
        username: string;
    } | undefined;
    addUserToRoom(roomId: string, user: User): Room | null;
    removeUserFromRoom(roomId: string, userId: string): Room | null;
    setNewHost(roomId: string, newHostId: string): Room | null;
    updateGameState(roomId: string, gameStateUpdates: Partial<GameState>): Room | null;
    getActiveRooms(): Room[];
    getEmptyRooms(): Room[];
    getRoomsOlderThan(minutes: number): Room[];
}
export declare const roomsManager: RoomManager;
export {};
//# sourceMappingURL=room-management.d.ts.map