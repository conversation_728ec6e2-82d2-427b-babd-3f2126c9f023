import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import MultiplayerLobby from '@/components/multiplayer/MultiplayerLobby';

export const metadata: Metadata = {
  title: 'Multiplayer Flag Guessing Game',
  description: 'Play flag guessing games with friends in real-time multiplayer mode',
};

export default function MultiplayerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Single Player</span>
            </Link>
          </div>

          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              🌍 Multiplayer Flag Game
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Challenge your friends in real-time flag guessing battles!
              Create or join a room to start playing together.
            </p>
          </div>
        </div>
        
        <MultiplayerLobby />
      </div>
    </div>
  );
}
