import { Metadata } from 'next';
import MultiplayerLobby from '@/components/multiplayer/MultiplayerLobby';

export const metadata: Metadata = {
  title: 'Multiplayer Flag Guessing Game',
  description: 'Play flag guessing games with friends in real-time multiplayer mode',
};

export default function MultiplayerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            🌍 Multiplayer Flag Game
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Challenge your friends in real-time flag guessing battles! 
            Create or join a room to start playing together.
          </p>
        </div>
        
        <MultiplayerLobby />
      </div>
    </div>
  );
}
