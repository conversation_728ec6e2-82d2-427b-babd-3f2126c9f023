import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import MultiplayerLobby from '@/components/multiplayer/MultiplayerLobby';

export const metadata: Metadata = {
  title: 'Multiplayer Flag Guessing Game',
  description: 'Play flag guessing games with friends in real-time multiplayer mode',
};

export default function MultiplayerPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <Link 
            href="/"
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Single Player</span>
          </Link>
        </div>
        
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-2">
            🌍 Multiplayer Mode
          </h1>
          <p className="text-muted-foreground">
            Challenge your friends in real-time flag guessing battles
          </p>
        </div>
      </div>
      
      <MultiplayerLobby />
    </div>
  );
}
