"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSocket } from '@/lib/context/SocketContext';
import { SOCKET_ACTIONS } from '@/lib/types/socket';
import { Room, User } from '@/lib/types/multiplayer';
import RoomLobby from './RoomLobby';
import GameView from './GameView';
import GameResults from './GameResults';

export default function MultiplayerRoom() {
  const router = useRouter();
  const { socket, isConnected } = useSocket();
  const [room, setRoom] = useState<Room | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [gameResults, setGameResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get session data from localStorage
    const sessionData = localStorage.getItem('multiplayer_session');
    if (!sessionData) {
      router.push('/multiplayer');
      return;
    }

    const { sessionToken, username, roomCode, isHost } = JSON.parse(sessionData);

    if (socket && isConnected) {
      // Join or create room based on session data
      if (isHost) {
        socket.emit(SOCKET_ACTIONS.CREATE_ROOM, {
          sessionToken,
          username,
          roomCode,
          settings: {
            maxRoomSize: 4,
            difficulty: 'medium',
            questionsCount: 10,
            timePerQuestion: 15
          }
        });
      } else {
        socket.emit(SOCKET_ACTIONS.JOIN_ROOM, {
          sessionToken,
          username,
          roomCode
        });
      }
    }
  }, [socket, isConnected, router]);

  useEffect(() => {
    if (!socket) return;

    // Socket event listeners
    const handleRoomUpdate = (updatedRoom: Room) => {
      setRoom(updatedRoom);
    };

    const handleUserUpdate = (updatedUser: User) => {
      setUser(updatedUser);
    };

    const handleGameResults = (results: any) => {
      setGameResults(results);
    };

    const handleError = (errorMessage: string) => {
      setError(errorMessage);
      if (errorMessage.includes('Room not found') || errorMessage.includes('Room is full')) {
        setTimeout(() => router.push('/multiplayer'), 2000);
      }
    };

    socket.on(SOCKET_ACTIONS.ROOM_UPDATE, handleRoomUpdate);
    socket.on(SOCKET_ACTIONS.USER_UPDATE, handleUserUpdate);
    socket.on(SOCKET_ACTIONS.GAME_RESULTS, handleGameResults);
    socket.on(SOCKET_ACTIONS.ERROR, handleError);

    return () => {
      socket.off(SOCKET_ACTIONS.ROOM_UPDATE, handleRoomUpdate);
      socket.off(SOCKET_ACTIONS.USER_UPDATE, handleUserUpdate);
      socket.off(SOCKET_ACTIONS.GAME_RESULTS, handleGameResults);
      socket.off(SOCKET_ACTIONS.ERROR, handleError);
    };
  }, [socket, router]);

  const handleLeaveRoom = () => {
    if (socket) {
      socket.emit(SOCKET_ACTIONS.LEAVE_ROOM);
    }
    localStorage.removeItem('multiplayer_session');
    router.push('/multiplayer');
  };

  // Loading state
  if (!isConnected || !room || !user) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🔄</div>
          <h2 className="text-xl font-semibold text-foreground mb-2">
            {error ? 'Connection Error' : 'Connecting...'}
          </h2>
          <p className="text-muted-foreground">
            {error || 'Setting up your multiplayer room'}
          </p>
          {error && (
            <p className="text-sm text-red-600 mt-2">
              Redirecting to lobby...
            </p>
          )}
        </div>
      </div>
    );
  }

  // Render appropriate component based on game state
  if (room.gameState.status === 'lobby') {
    return (
      <RoomLobby 
        room={room} 
        user={user} 
        onLeaveRoom={handleLeaveRoom}
      />
    );
  }

  if (room.gameState.status === 'playing') {
    return (
      <GameView 
        room={room} 
        user={user} 
        onLeaveRoom={handleLeaveRoom}
      />
    );
  }

  if (room.gameState.status === 'finished') {
    return (
      <GameResults 
        room={room} 
        user={user} 
        results={gameResults}
        onLeaveRoom={handleLeaveRoom}
      />
    );
  }

  return null;
}
