"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSocket } from '@/lib/context/SocketContext';
import { SOCKET_ACTIONS } from '@/lib/types/socket';
import { Room, User, GameQuestion, ChatMessage, ServerMessage } from '@/lib/types/multiplayer';
import RoomLobby from './RoomLobby';
import GameView from './GameView';
import GameResults from './GameResults';

type RoomState = 'connecting' | 'lobby' | 'game' | 'results' | 'error';

export default function MultiplayerRoom() {
  const router = useRouter();
  const { socket, room, user, isConnecting } = useSocket();
  const [roomState, setRoomState] = useState<RoomState>('connecting');
  const [error, setError] = useState<string | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<GameQuestion | null>(null);
  const [messages, setMessages] = useState<(ChatMessage | ServerMessage)[]>([]);
  const [gameResults, setGameResults] = useState<any>(null);

  useEffect(() => {
    if (!socket || isConnecting) return;

    // Get stored data from localStorage
    const username = localStorage.getItem('username');
    const roomAction = localStorage.getItem('roomAction');
    const roomSettings = localStorage.getItem('roomSettings');
    const roomId = localStorage.getItem('roomId');
    const inviteCode = localStorage.getItem('inviteCode');
    const roomPasscode = localStorage.getItem('roomPasscode');

    if (!username || !roomAction) {
      setError('Missing required data');
      setRoomState('error');
      return;
    }

    // Handle room creation or joining
    if (roomAction === 'create' && roomSettings) {
      const settings = JSON.parse(roomSettings);
      socket.emit(SOCKET_ACTIONS.CREATE_ROOM, username, settings.roomName, {
        difficulty: settings.difficulty,
        questionCount: settings.questionCount,
        timePerQuestion: settings.timePerQuestion,
        allowSpectators: settings.allowSpectators,
      }, (response: any) => {
        if (response.error) {
          setError(response.error);
          setRoomState('error');
        } else {
          setRoomState('lobby');
          // Clear localStorage after successful creation
          localStorage.removeItem('roomAction');
          localStorage.removeItem('roomSettings');
        }
      });
    } else if (roomAction === 'join') {
      if (roomId) {
        socket.emit(SOCKET_ACTIONS.JOIN_ROOM, roomId, username, (response: any) => {
          if (!response.success) {
            setError(response.error || 'Failed to join room');
            setRoomState('error');
          } else {
            setRoomState('lobby');
            // Clear localStorage after successful join
            localStorage.removeItem('roomAction');
            localStorage.removeItem('roomId');
          }
        });
      } else if (inviteCode) {
        socket.emit(SOCKET_ACTIONS.JOIN_ROOM_BY_INVITE, inviteCode, username, roomPasscode || '', (response: any) => {
          if (!response.success) {
            setError(response.error || 'Failed to join room');
            setRoomState('error');
          } else {
            setRoomState('lobby');
            // Clear localStorage after successful join
            localStorage.removeItem('roomAction');
            localStorage.removeItem('inviteCode');
            localStorage.removeItem('roomPasscode');
          }
        });
      } else {
        setError('No room ID or invite code provided');
        setRoomState('error');
      }
    }
  }, [socket, isConnecting]);

  useEffect(() => {
    if (!socket) return;

    // Game event listeners
    socket.on(SOCKET_ACTIONS.START_GAME, () => {
      setRoomState('game');
      setCurrentQuestion(null);
      setGameResults(null);
    });

    socket.on(SOCKET_ACTIONS.NEW_QUESTION, (question: GameQuestion) => {
      setCurrentQuestion(question);
    });

    socket.on(SOCKET_ACTIONS.END_GAME, () => {
      setRoomState('results');
      setCurrentQuestion(null);
    });

    socket.on(SOCKET_ACTIONS.GAME_RESULTS, (results: any) => {
      setGameResults(results);
    });

    socket.on(SOCKET_ACTIONS.QUESTION_TIMEOUT, () => {
      // Handle question timeout
    });

    socket.on(SOCKET_ACTIONS.SHOW_RESULTS, (answers: any[]) => {
      // Handle showing question results
    });

    // Chat event listeners
    socket.on(SOCKET_ACTIONS.USER_MESSAGE, (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
    });

    socket.on(SOCKET_ACTIONS.SERVER_MESSAGE, (message: ServerMessage) => {
      setMessages(prev => [...prev, message]);
    });

    // Error handling
    socket.on(SOCKET_ACTIONS.KICK_USER, () => {
      setError('You have been kicked from the room');
      setRoomState('error');
    });

    return () => {
      socket.off(SOCKET_ACTIONS.START_GAME);
      socket.off(SOCKET_ACTIONS.NEW_QUESTION);
      socket.off(SOCKET_ACTIONS.END_GAME);
      socket.off(SOCKET_ACTIONS.GAME_RESULTS);
      socket.off(SOCKET_ACTIONS.QUESTION_TIMEOUT);
      socket.off(SOCKET_ACTIONS.SHOW_RESULTS);
      socket.off(SOCKET_ACTIONS.USER_MESSAGE);
      socket.off(SOCKET_ACTIONS.SERVER_MESSAGE);
      socket.off(SOCKET_ACTIONS.KICK_USER);
    };
  }, [socket]);

  const handleLeaveRoom = () => {
    if (socket) {
      socket.emit(SOCKET_ACTIONS.LEAVE_ROOM);
    }
    // Clear any remaining localStorage data
    localStorage.removeItem('sessionToken');
    localStorage.removeItem('username');
    localStorage.removeItem('roomAction');
    localStorage.removeItem('roomSettings');
    localStorage.removeItem('roomId');
    localStorage.removeItem('inviteCode');
    localStorage.removeItem('roomPasscode');
    router.push('/multiplayer');
  };

  const sendMessage = (message: string) => {
    if (socket && room) {
      socket.emit(SOCKET_ACTIONS.USER_MESSAGE, message, room.id);
    }
  };

  if (isConnecting || roomState === 'connecting') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Connecting to multiplayer server...</p>
        </div>
      </div>
    );
  }

  if (roomState === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-6xl mb-4">😞</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Oops! Something went wrong</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={handleLeaveRoom}
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
          >
            Back to Lobby
          </button>
        </div>
      </div>
    );
  }

  if (!room || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading room data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {roomState === 'lobby' && (
        <RoomLobby
          room={room}
          user={user}
          messages={messages}
          onSendMessage={sendMessage}
          onLeaveRoom={handleLeaveRoom}
        />
      )}

      {roomState === 'game' && currentQuestion && (
        <GameView
          room={room}
          user={user}
          currentQuestion={currentQuestion}
          onLeaveRoom={handleLeaveRoom}
        />
      )}

      {roomState === 'results' && gameResults && (
        <GameResults
          room={room}
          user={user}
          results={gameResults}
          onLeaveRoom={handleLeaveRoom}
        />
      )}
    </div>
  );
}
