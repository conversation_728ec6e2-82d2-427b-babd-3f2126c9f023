{"name": "flags-game-server", "version": "1.0.0", "description": "WebSocket server for multiplayer flag guessing game", "main": "dist/app.js", "scripts": {"dev": "tsx src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src --ext .ts"}, "dependencies": {"socket.io": "^4.8.1", "express": "^4.18.2", "cors": "^2.8.5", "nanoid": "^5.1.5", "uuid": "^11.1.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^10.0.0", "tsx": "^4.20.3", "typescript": "^5.0.0"}}