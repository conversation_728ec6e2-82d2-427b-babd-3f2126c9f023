{"name": "flags-game-server", "version": "1.0.0", "description": "WebSocket server for multiplayer flag guessing game", "main": "dist/app.js", "scripts": {"dev": "tsx src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src --ext .ts"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "nanoid": "^5.1.5", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.0.0", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "tsx": "^4.20.3", "typescript": "^5.0.0"}}