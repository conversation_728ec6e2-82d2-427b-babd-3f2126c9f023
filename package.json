{"name": "guess-the-country", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "server:dev": "cd server && npm run dev", "server:build": "cd server && npm run build", "server:start": "cd server && npm run start", "dev:multiplayer": "echo \"Please run 'npm run server:dev' in one terminal and 'npm run dev' in another, or use start-multiplayer.bat\"", "build:full": "npm run build && npm run server:build", "start:full": "npm run start & npm run server:start"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "nanoid": "^5.1.5", "next": "15.3.3", "posthog-js": "^1.256.1", "posthog-node": "^5.1.1", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}