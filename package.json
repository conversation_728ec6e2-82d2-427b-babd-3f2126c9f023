{"name": "guess-the-country", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:ws-server": "tsx websocket-server.ts", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "nanoid": "^5.1.5", "next": "15.3.3", "posthog-js": "^1.256.1", "posthog-node": "^5.1.1", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "ws": "^8.18.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}