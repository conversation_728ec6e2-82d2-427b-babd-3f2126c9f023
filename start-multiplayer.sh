#!/bin/bash

echo "Starting Multiplayer Flag Guessing Game..."
echo ""

echo "Starting WebSocket Server (port 3001)..."
cd server && npm run dev &
SERVER_PID=$!

sleep 3

echo "Starting Next.js App (port 3000)..."
cd .. && npm run dev &
APP_PID=$!

echo ""
echo "Both services are starting..."
echo "- Next.js App: http://localhost:3000"
echo "- WebSocket Server: http://localhost:3001"
echo ""
echo "Press Ctrl+C to stop both services..."

# Wait for user to stop
trap "kill $SERVER_PID $APP_PID; exit" INT
wait
