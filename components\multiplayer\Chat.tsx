"use client";

import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage, ServerMessage, ServerMessageType } from '@/lib/types/multiplayer';

interface ChatProps {
  messages: (ChatMessage | ServerMessage)[];
  onSendMessage: (message: string) => void;
}

export default function Chat({ messages, onSendMessage }: ChatProps) {
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && newMessage.length <= 500) {
      onSendMessage(newMessage.trim());
      setNewMessage('');
      inputRef.current?.focus();
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getServerMessageIcon = (type: ServerMessageType) => {
    switch (type) {
      case ServerMessageType.USER_JOINED:
        return '👋';
      case ServerMessageType.USER_LEFT:
        return '👋';
      case ServerMessageType.USER_DISCONNECTED:
        return '📡';
      case ServerMessageType.USER_RECONNECTED:
        return '🔄';
      case ServerMessageType.NEW_HOST:
        return '👑';
      case ServerMessageType.GAME_STARTED:
        return '🚀';
      case ServerMessageType.GAME_ENDED:
        return '🏁';
      case ServerMessageType.QUESTION_STARTED:
        return '❓';
      case ServerMessageType.QUESTION_ENDED:
        return '⏰';
      case ServerMessageType.PLAYER_ANSWERED:
        return '💭';
      default:
        return 'ℹ️';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg h-96 flex flex-col">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-800 flex items-center">
          💬 Chat
          <span className="ml-2 text-sm text-gray-500">({messages.length})</span>
        </h3>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 text-sm">
            No messages yet. Say hello! 👋
          </div>
        ) : (
          messages.map((message, index) => (
            <div key={index} className="text-sm">
              {'type' in message ? (
                // Server message
                <div className="flex items-start space-x-2 text-gray-600">
                  <span className="text-lg">{getServerMessageIcon(message.type)}</span>
                  <div className="flex-1">
                    <div className="bg-gray-100 rounded-lg p-2">
                      <p className="text-xs text-gray-500 mb-1">
                        {formatTime(message.timestamp)}
                      </p>
                      <p>{message.message}</p>
                    </div>
                  </div>
                </div>
              ) : (
                // User message
                <div className="flex items-start space-x-2">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium flex-shrink-0"
                    style={{ backgroundColor: message.color }}
                  >
                    {message.username.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="bg-blue-50 rounded-lg p-2">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-1">
                          <span className="font-medium text-gray-800 text-xs">
                            {message.username}
                          </span>
                          {message.isAdmin && (
                            <span className="text-red-500 text-xs">🛡️</span>
                          )}
                        </div>
                        <span className="text-xs text-gray-500">
                          {formatTime(message.timestamp)}
                        </span>
                      </div>
                      <p className="text-gray-700 break-words">{message.message}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            maxLength={500}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || newMessage.length > 500}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium"
          >
            Send
          </button>
        </form>
        <div className="mt-1 text-xs text-gray-500 text-right">
          {newMessage.length}/500
        </div>
      </div>
    </div>
  );
}
