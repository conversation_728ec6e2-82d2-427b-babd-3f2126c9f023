"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.usersManager = exports.validateUsername = exports.isUserAdmin = exports.requestIsNotFromHost = void 0;
const room_management_1 = require("./room-management");
// Generate a random color for users
const generateUserColor = () => {
    const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
};
class UserManager {
    constructor() {
        this.users = new Map();
    }
    createUser(params) {
        const user = {
            id: params.id,
            socketId: params.socketId,
            username: params.username,
            roomId: params.roomId,
            created: new Date().toISOString(),
            color: generateUserColor(),
            isAdmin: params.isAdmin || false,
            score: 0,
            isReady: false,
        };
        this.users.set(user.id, user);
        return user;
    }
    get(userId) {
        return this.users.get(userId);
    }
    has(userId) {
        return this.users.has(userId);
    }
    set(userId, user) {
        this.users.set(userId, user);
    }
    delete(userId) {
        return this.users.delete(userId);
    }
    setUsers(users) {
        this.users = users;
    }
    getLength() {
        return this.users.size;
    }
    updateUser(userId, updates) {
        const user = this.get(userId);
        if (!user)
            return null;
        const updatedUser = { ...user, ...updates };
        this.set(userId, updatedUser);
        return updatedUser;
    }
    updateUserScore(userId, points) {
        const user = this.get(userId);
        if (!user)
            return null;
        const updatedUser = { ...user, score: user.score + points };
        this.set(userId, updatedUser);
        return updatedUser;
    }
    setUserReady(userId, isReady) {
        const user = this.get(userId);
        if (!user)
            return null;
        const updatedUser = { ...user, isReady };
        this.set(userId, updatedUser);
        return updatedUser;
    }
    resetUserScore(userId) {
        const user = this.get(userId);
        if (!user)
            return null;
        const updatedUser = { ...user, score: 0 };
        this.set(userId, updatedUser);
        return updatedUser;
    }
    getUsersByRoom(roomId) {
        return Array.from(this.users.values()).filter(user => user.roomId === roomId);
    }
    removeUserFromRoom(userId) {
        const user = this.get(userId);
        if (user) {
            // Remove user from room members
            room_management_1.roomsManager.removeUserFromRoom(user.roomId, userId);
            // Delete user from users map
            this.delete(userId);
        }
    }
    getInactiveUsers(minutes) {
        const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
        return Array.from(this.users.values()).filter(user => {
            const userCreated = new Date(user.created);
            return userCreated < cutoffTime;
        });
    }
}
// Helper function to check if request is from room host
const requestIsNotFromHost = (socket, rooms) => {
    if (!socket.data?.roomId || !socket.data?.userId)
        return true;
    const room = rooms.get(socket.data.roomId);
    if (!room)
        return true;
    return room.host !== socket.data.userId;
};
exports.requestIsNotFromHost = requestIsNotFromHost;
// Helper function to check if user is admin
const isUserAdmin = (socket) => {
    return socket.data?.isAdmin === true;
};
exports.isUserAdmin = isUserAdmin;
// Helper function to validate username
const validateUsername = (username) => {
    if (typeof username !== 'string') {
        return { valid: false, error: 'Username must be a string' };
    }
    if (username.length === 0) {
        return { valid: false, error: 'Username cannot be empty' };
    }
    if (username.length > 20) {
        return { valid: false, error: 'Username cannot exceed 20 characters' };
    }
    // Check for inappropriate content (basic filter)
    const inappropriateWords = ['admin', 'moderator', 'bot'];
    const lowerUsername = username.toLowerCase();
    for (const word of inappropriateWords) {
        if (lowerUsername.includes(word)) {
            return { valid: false, error: 'Username contains inappropriate content' };
        }
    }
    return { valid: true };
};
exports.validateUsername = validateUsername;
exports.usersManager = new UserManager();
//# sourceMappingURL=user-management.js.map