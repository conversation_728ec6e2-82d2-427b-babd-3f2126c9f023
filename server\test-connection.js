// Simple test script to verify WebSocket server is working
const { io } = require('socket.io-client');

const socket = io('http://localhost:3001', {
  auth: {
    token: 'test_user_123'
  }
});

socket.on('connect', () => {
  console.log('✅ Connected to server successfully!');
  console.log('Socket ID:', socket.id);
  
  // Test creating a room
  socket.emit('CREATE_ROOM',
    'TestUser',           // username
    'Test Room',          // roomName
    {                     // settings
      maxRoomSize: 4,
      difficulty: 'medium',
      questionsCount: 10,
      timePerQuestion: 15
    },
    (response) => {
    console.log('Create room response:', response);
    
    // Disconnect after test
    setTimeout(() => {
      socket.disconnect();
      console.log('✅ Test completed successfully!');
      process.exit(0);
    }, 1000);
  });
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection failed:', error.message);
  process.exit(1);
});

socket.on('ROOM_UPDATE', (room) => {
  console.log('📢 Room update received:', {
    id: room.id,
    name: room.name,
    code: room.code,
    members: room.members.length,
    status: room.gameState.status
  });
});

socket.on('USER_UPDATE', (user) => {
  console.log('👤 User update received:', {
    id: user.id,
    username: user.username,
    score: user.score,
    isReady: user.isReady
  });
});

socket.on('ERROR', (error) => {
  console.error('❌ Server error:', error);
});

console.log('🔄 Attempting to connect to WebSocket server...');

// Timeout after 10 seconds
setTimeout(() => {
  console.error('❌ Connection timeout');
  process.exit(1);
}, 10000);
