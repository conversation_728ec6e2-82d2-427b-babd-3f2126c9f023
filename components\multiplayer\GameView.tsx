"use client";

import React, { useState, useEffect } from 'react';
import { useSocket } from '@/lib/context/SocketContext';
import { SOCKET_ACTIONS } from '@/lib/types/socket';
import { Room, User, GameQuestion } from '@/lib/types/multiplayer';

interface GameViewProps {
  room: Room;
  user: User;
  currentQuestion: GameQuestion;
  onLeaveRoom: () => void;
}

export default function GameView({ room, user, currentQuestion, onLeaveRoom }: GameViewProps) {
  const { socket } = useSocket();
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [hasAnswered, setHasAnswered] = useState(false);
  const [timeLeft, setTimeLeft] = useState(currentQuestion.timeLimit);
  const [showResults, setShowResults] = useState(false);

  const isHost = user.id === room.host;

  useEffect(() => {
    // Reset state for new question
    setSelectedAnswer('');
    setHasAnswered(false);
    setTimeLeft(currentQuestion.timeLimit);
    setShowResults(false);

    // Start countdown timer
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [currentQuestion]);

  useEffect(() => {
    if (!socket) return;

    socket.on(SOCKET_ACTIONS.SHOW_RESULTS, () => {
      setShowResults(true);
    });

    socket.on(SOCKET_ACTIONS.QUESTION_TIMEOUT, () => {
      setShowResults(true);
    });

    return () => {
      socket.off(SOCKET_ACTIONS.SHOW_RESULTS);
      socket.off(SOCKET_ACTIONS.QUESTION_TIMEOUT);
    };
  }, [socket]);

  const handleAnswerSelect = (answer: string) => {
    if (hasAnswered || timeLeft <= 0) return;

    setSelectedAnswer(answer);
    setHasAnswered(true);

    if (socket) {
      socket.emit(SOCKET_ACTIONS.SUBMIT_ANSWER, answer);
    }
  };

  const handleNextQuestion = () => {
    if (socket && isHost) {
      socket.emit(SOCKET_ACTIONS.NEXT_QUESTION);
    }
  };

  const handleEndGame = () => {
    if (socket && isHost) {
      socket.emit(SOCKET_ACTIONS.END_GAME);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Game Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                Question {currentQuestion.questionNumber} of {room.gameState.totalQuestions}
              </h1>
              <p className="text-gray-600">Room: {room.name} • Difficulty: {room.settings.difficulty}</p>
            </div>
            <button
              onClick={onLeaveRoom}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Leave Game
            </button>
          </div>

          {/* Timer */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Time Remaining</span>
              <span className={`text-2xl font-bold ${
                timeLeft > 10 ? 'text-green-600' : timeLeft > 5 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {timeLeft}s
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-1000 ${
                  timeLeft > 10 ? 'bg-green-500' : timeLeft > 5 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${((currentQuestion.timeLimit - timeLeft) / currentQuestion.timeLimit) * 100}%` }}
              />
            </div>
          </div>

          {/* Host Controls */}
          {isHost && showResults && (
            <div className="flex space-x-3 mb-4">
              <button
                onClick={handleNextQuestion}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Next Question
              </button>
              <button
                onClick={handleEndGame}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                End Game
              </button>
            </div>
          )}
        </div>

        {/* Flag Display */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-6">
          <div className="text-center mb-8">
                <div className="text-8xl mb-4">
                  {currentQuestion.currentCountry.flag}
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  Which country does this flag belong to?
                </h2>
                {showResults && (
                  <p className="text-lg text-green-600 font-medium">
                    Answer: {currentQuestion.currentCountry.name}
                  </p>
                )}
              </div>

          {/* Answer Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {currentQuestion.options.map((option, index) => {
              const isSelected = selectedAnswer === option.name;
              const isCorrect = option.name === currentQuestion.currentCountry.name;

              let buttonClass = 'w-full p-4 text-left border-2 rounded-lg transition-all font-medium ';

              if (showResults) {
                if (isCorrect) {
                  buttonClass += 'border-green-500 bg-green-50 text-green-700';
                } else if (isSelected && !isCorrect) {
                  buttonClass += 'border-red-500 bg-red-50 text-red-700';
                } else {
                  buttonClass += 'border-gray-300 bg-gray-50 text-gray-600';
                }
              } else if (isSelected) {
                buttonClass += 'border-blue-500 bg-blue-50 text-blue-700';
              } else if (hasAnswered) {
                buttonClass += 'border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed';
              } else {
                buttonClass += 'border-gray-300 hover:border-blue-400 hover:bg-blue-50 text-gray-700';
              }

              return (
                <button
                  key={index}
                  onClick={() => handleAnswerSelect(option.name)}
                  disabled={hasAnswered || timeLeft <= 0}
                  className={buttonClass}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{option.flag}</span>
                    <span className="text-lg">{option.name}</span>
                  </div>
                  {showResults && isCorrect && (
                    <span className="float-right text-green-600">✓</span>
                  )}
                  {showResults && isSelected && !isCorrect && (
                    <span className="float-right text-red-600">✗</span>
                  )}
                </button>
              );
            })}
              </div>

          {/* Answer Status */}
          <div className="text-center">
            {hasAnswered && !showResults && (
              <p className="text-green-600 font-medium">
                ✅ Answer submitted! Waiting for other players...
              </p>
            )}
            {timeLeft <= 0 && !hasAnswered && (
              <p className="text-red-600 font-medium">
                ⏰ Time's up!
              </p>
            )}
          </div>
        </div>

        {/* Players Scores */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="font-semibold text-gray-800 mb-4">Leaderboard</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {room.members
              .sort((a, b) => b.score - a.score)
              .map((member, index) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-600">
                      #{index + 1}
                    </span>
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                      style={{ backgroundColor: member.color }}
                    >
                      {member.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">{member.username}</span>
                        {member.id === room.host && <span className="text-yellow-500">👑</span>}
                      </div>
                      <span className="text-sm font-bold text-blue-600">
                        {member.score} points
                      </span>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
