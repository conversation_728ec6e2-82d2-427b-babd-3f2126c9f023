"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSocketEvents = void 0;
const nanoid_1 = require("nanoid");
const socket_1 = require("../../../lib/types/socket");
const room_management_1 = require("../utils/room-management");
const user_management_1 = require("../utils/user-management");
const roomTimeouts = {};
const handleSocketEvents = (io, socket) => {
    const userId = socket.handshake.auth.token;
    const adminTokenHandshake = socket.handshake.auth.adminToken;
    const adminToken = process.env.ADMIN_TOKEN;
    if (!userId) {
        socket.disconnect();
        return;
    }
    // Check if user is already connected
    if (user_management_1.usersManager.users.has(userId)) {
        socket.disconnect();
        return;
    }
    // Initialize socket data
    socket.data = {
        userId: userId || '',
        roomId: '',
        isAdmin: false,
    };
    if (typeof socket.data.userId !== 'string')
        return;
    // Check for admin privileges
    if (adminToken === adminTokenHandshake) {
        socket.data.isAdmin = true;
        socket.emit(socket_1.SOCKET_ACTIONS.SET_ADMIN);
    }
    console.log(`⚡️ New user connected - User Id: ${userId}`);
    // Room management events
    socket.on(socket_1.SOCKET_ACTIONS.CHECK_IF_ROOM_EXISTS, (roomId, callback) => {
        const room = room_management_1.roomsManager.get(roomId);
        typeof callback === 'function' && callback(room ?? null);
    });
    socket.on(socket_1.SOCKET_ACTIONS.CREATE_ROOM, async (username, roomName, settings, callback) => {
        const usernameValidation = (0, user_management_1.validateUsername)(username);
        if (!usernameValidation.valid) {
            typeof callback === 'function' && callback({ error: usernameValidation.error });
            return;
        }
        const newRoomId = (0, nanoid_1.nanoid)(6);
        if (userId && socket.data.userId) {
            const existingRoom = room_management_1.roomsManager.get(newRoomId);
            if (existingRoom) {
                typeof callback === 'function' && callback({ error: 'Room already exists' });
                return;
            }
            const user = user_management_1.usersManager.createUser({
                id: socket.data.userId,
                username,
                roomId: newRoomId,
                socketId: socket.id,
                isAdmin: socket.data.isAdmin,
            });
            console.log(`👀 New user created room: ${newRoomId} - User Id: ${userId}`);
            socket.join(newRoomId);
            socket.data.roomId = newRoomId;
            const newRoom = room_management_1.roomsManager.create(newRoomId, roomName, user, settings);
            if (newRoom) {
                typeof callback === 'function' && callback({ result: newRoom });
                socket.emit(socket_1.SOCKET_ACTIONS.GET_ROOM_INFO, newRoom);
                socket.emit(socket_1.SOCKET_ACTIONS.GET_USER_INFO, user);
            }
        }
        else {
            typeof callback === 'function' && callback({ error: 'Failed to create room' });
        }
    });
    socket.on(socket_1.SOCKET_ACTIONS.JOIN_ROOM, (roomId, username, callback) => {
        const usernameValidation = (0, user_management_1.validateUsername)(username);
        if (!usernameValidation.valid) {
            typeof callback === 'function' && callback({
                success: false,
                error: usernameValidation.error
            });
            return;
        }
        if (!roomId || !socket.data.userId) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Invalid room ID or user ID'
            });
            return;
        }
        if (roomId.length !== 6) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Room ID must be 6 characters long'
            });
            return;
        }
        const existingRoom = room_management_1.roomsManager.has(roomId);
        if (!existingRoom) {
            typeof callback === 'function' && callback({
                success: false,
                error: `Room ${roomId} not found`
            });
            return;
        }
        const updatedRoom = addUserToRoom(io, socket, socket.data.userId, roomId, username);
        if (updatedRoom && typeof callback === 'function') {
            callback({ success: true });
        }
    });
    socket.on(socket_1.SOCKET_ACTIONS.JOIN_ROOM_BY_INVITE, (inviteCode, username, roomPasscode, callback) => {
        const usernameValidation = (0, user_management_1.validateUsername)(username);
        if (!usernameValidation.valid) {
            typeof callback === 'function' && callback({
                success: false,
                error: usernameValidation.error
            });
            return;
        }
        if (!inviteCode || !socket.data.userId) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Invalid invite code or user ID'
            });
            return;
        }
        if (inviteCode.length !== 5) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Invite code must be 5 characters long'
            });
            return;
        }
        const room = room_management_1.roomsManager.getRoomByInviteCode(inviteCode);
        if (!room) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Invalid invite code or room no longer exists'
            });
            return;
        }
        if (room.passcode && roomPasscode.trim() !== room.passcode) {
            typeof callback === 'function' && callback({
                success: false,
                error: 'Incorrect passcode'
            });
            return;
        }
        const updatedRoom = addUserToRoom(io, socket, socket.data.userId, room.id, username);
        if (updatedRoom && typeof callback === 'function') {
            callback({ success: true, roomId: room.id });
        }
    });
    // Handle toggle ready state
    socket.on('TOGGLE_READY', () => {
        if (!socket.data.userId || !socket.data.roomId)
            return;
        const user = user_management_1.usersManager.get(socket.data.userId);
        const room = room_management_1.roomsManager.get(socket.data.roomId);
        if (!user || !room)
            return;
        user.isReady = !user.isReady;
        user_management_1.usersManager.set(user.id, user);
        // Update room members
        const memberIndex = room.members.findIndex(m => m.id === user.id);
        if (memberIndex !== -1) {
            room.members[memberIndex] = user;
            room_management_1.roomsManager.set(room.id, room);
            // Broadcast room update
            io.to(room.id).emit('ROOM_UPDATE', room);
        }
    });
    // Handle start game
    socket.on('START_GAME', () => {
        if (!socket.data.userId || !socket.data.roomId)
            return;
        const user = user_management_1.usersManager.get(socket.data.userId);
        const room = room_management_1.roomsManager.get(socket.data.roomId);
        if (!user || !room || user.id !== room.host)
            return;
        // Check if all players are ready
        const allReady = room.members.every(member => member.isReady);
        if (!allReady || room.members.length < 2)
            return;
        // Start the game
        room.gameState.status = 'playing';
        room.gameState.currentQuestion = 1;
        room.gameState.startTime = Date.now();
        // Reset scores
        room.members.forEach(member => {
            member.score = 0;
            user_management_1.usersManager.set(member.id, member);
        });
        room_management_1.roomsManager.set(room.id, room);
        // Broadcast game start
        io.to(room.id).emit('ROOM_UPDATE', room);
        io.to(room.id).emit('GAME_STARTED');
    });
    // Handle answer submission
    socket.on('SUBMIT_ANSWER', (data) => {
        if (!socket.data.userId || !socket.data.roomId)
            return;
        const { answer, timeRemaining } = data;
        const user = user_management_1.usersManager.get(socket.data.userId);
        const room = room_management_1.roomsManager.get(socket.data.roomId);
        if (!user || !room || room.gameState.status !== 'playing')
            return;
        // Calculate score based on correctness and time
        const isCorrect = answer === room.gameState.currentCountry?.name;
        const timeBonus = Math.max(0, timeRemaining * 2);
        const points = isCorrect ? 100 + timeBonus : 0;
        user.score += points;
        user_management_1.usersManager.set(user.id, user);
        // Update room members
        const memberIndex = room.members.findIndex(m => m.id === user.id);
        if (memberIndex !== -1) {
            room.members[memberIndex] = user;
        }
        // Check if all players have answered
        const answeredCount = room.gameState.answers?.length || 0;
        if (answeredCount >= room.members.length) {
            // Move to next question or end game
            if (room.gameState.currentQuestion >= room.gameState.totalQuestions) {
                // End game
                room.gameState.status = 'finished';
                room.gameState.endTime = Date.now();
                // Calculate final results
                const results = room.members
                    .sort((a, b) => b.score - a.score)
                    .map((member, index) => ({
                    ...member,
                    rank: index + 1
                }));
                room_management_1.roomsManager.set(room.id, room);
                io.to(room.id).emit('ROOM_UPDATE', room);
                io.to(room.id).emit('GAME_RESULTS', results);
            }
            else {
                // Next question
                room.gameState.currentQuestion++;
                room.gameState.answers = [];
                room_management_1.roomsManager.set(room.id, room);
                io.to(room.id).emit('ROOM_UPDATE', room);
                io.to(room.id).emit('NEXT_QUESTION');
            }
        }
    });
    // Handle disconnect
    socket.on('disconnect', () => {
        if (socket.data.userId && socket.data.roomId) {
            const room = room_management_1.roomsManager.get(socket.data.roomId);
            if (room) {
                // Remove user from room
                room.members = room.members.filter(member => member.id !== socket.data.userId);
                if (room.members.length === 0) {
                    // Delete empty room
                    room_management_1.roomsManager.delete(room.id);
                }
                else {
                    // If host left, assign new host
                    if (room.host === socket.data.userId && room.members.length > 0) {
                        room.host = room.members[0].id;
                    }
                    room_management_1.roomsManager.set(room.id, room);
                    io.to(room.id).emit('ROOM_UPDATE', room);
                }
            }
            // Remove user
            user_management_1.usersManager.delete(socket.data.userId);
        }
    });
};
exports.handleSocketEvents = handleSocketEvents;
//# sourceMappingURL=socket-events.js.map