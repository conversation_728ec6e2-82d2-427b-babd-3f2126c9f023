import { GameQ<PERSON><PERSON>, PlayerAnswer, GameResults, Room } from '../../../lib/types/multiplayer';
import { Difficulty } from '../../../lib/constants';
interface Country {
    name: string;
    code: string;
    flag: string;
    region: string;
    difficulty?: Difficulty;
}
export declare class GameLogic {
    static getDifficultySettings(difficulty: Difficulty): {
        count: number;
        timePerQuestion: number;
        optionsCount: number;
    };
    static getCountriesByDifficulty(difficulty: Difficulty): Country[];
    static generateQuestion(difficulty: Difficulty, questionNumber: number): GameQuestion;
    static calculateScore(isCorrect: boolean, timeToAnswer: number, timeLimit: number): number;
    static processAnswer(answer: string, correctAnswer: string, timeToAnswer: number, timeLimit: number, userId: string): PlayerAnswer;
    static calculateFinalResults(room: Room): GameResults;
    static isGameComplete(room: Room): boolean;
    static areAllPlayersReady(room: Room): boolean;
    static getNextQuestionIndex(room: Room): number;
}
export {};
//# sourceMappingURL=game-logic.d.ts.map