import { User } from '../../../lib/types/multiplayer';
import { CustomSocket } from '../../../lib/types/socket';
interface CreateUserParams {
    id: string;
    username: string;
    roomId: string;
    socketId: string;
    isAdmin?: boolean;
}
declare class UserManager {
    users: Map<string, User>;
    createUser(params: CreateUserParams): User;
    get(userId: string): User | undefined;
    has(userId: string): boolean;
    set(userId: string, user: User): void;
    delete(userId: string): boolean;
    setUsers(users: Map<string, User>): void;
    getLength(): number;
    updateUser(userId: string, updates: Partial<User>): User | null;
    updateUserScore(userId: string, points: number): User | null;
    setUserReady(userId: string, isReady: boolean): User | null;
    resetUserScore(userId: string): User | null;
    getUsersByRoom(roomId: string): User[];
    removeUserFromRoom(userId: string): void;
    getInactiveUsers(minutes: number): User[];
}
export declare const requestIsNotFromHost: (socket: CustomSocket, rooms: Map<string, any>) => boolean;
export declare const isUserAdmin: (socket: CustomSocket) => boolean;
export declare const validateUsername: (username: string) => {
    valid: boolean;
    error?: string;
};
export declare const usersManager: UserManager;
export {};
//# sourceMappingURL=user-management.d.ts.map